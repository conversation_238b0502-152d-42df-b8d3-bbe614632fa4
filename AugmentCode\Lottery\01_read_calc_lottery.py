#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
读取彩票数据Excel文件程序
功能：读取指定Excel文件中特定标签页下特定范围内的数据，并保存为变量

具体功能：
1. 读取根目录下的"lottery_data_all.xlsx"文件
2. 从"SSQ_data_all"标签页提取：
   - A列、C列至H列数据 → ssqhistory_redramout（7列数据）
   - A列、I列至O列数据 → ssqhistory_allout（8列数据）
3. 从"DLT_data_all"标签页提取：
   - A列、C列至G列数据 → dlthistory_redramout（6列数据）
   - A列、H列至N列数据 → dlthistory_allout（8列数据）
4. 提取最新一期号码：
   - 从ssqhistory_allout最后一行的第2列至第8列 → ssq_lastNo
   - 从dlthistory_allout最后一行的第2列至第8列 → dlt_lastNo
5. 统计号码出现次数与概率：
   - 统计ssqhistory_allout中第2列至第7列红球号码(1-33)出现次数 → ssq_redtime
   - 统计ssqhistory_allout中第2列至第7列红球号码(1-33)出现概率 → ssq_redprob
   - 统计ssqhistory_allout中第8列蓝球号码(1-16)出现次数 → ssq_bluetime
   - 统计ssqhistory_allout中第8列蓝球号码(1-16)出现概率 → ssq_blueprob
   - 统计dlthistory_allout中第2列至第6列红球号码(1-35)出现次数 → dlt_redtime
   - 统计dlthistory_allout中第2列至第6列红球号码(1-35)出现概率 → dlt_redprob
   - 统计dlthistory_allout中第7列至第8列蓝球号码(1-12)出现次数 → dlt_bluetime
   - 统计dlthistory_allout中第7列至第8列蓝球号码(1-12)出现概率 → dlt_blueprob
6. 统计号码跟随性次数与概率：
   - 统计ssqhistory_allout中红球号码(1-33)的跟随次数 → ssq_redfollowtime (33×33矩阵)
   - 统计ssqhistory_allout中红球号码(1-33)的跟随概率 → ssq_redfollowprob (33×33矩阵)
   - 统计ssqhistory_allout中蓝球号码(1-16)的跟随次数 → ssq_bluefollowtime (16×16矩阵)
   - 统计ssqhistory_allout中蓝球号码(1-16)的跟随概率 → ssq_bluefollowprob (16×16矩阵)
   - 统计dlthistory_allout中红球号码(1-35)的跟随次数 → dlt_redfollowtime (35×35矩阵)
   - 统计dlthistory_allout中红球号码(1-35)的跟随概率 → dlt_redfollowprob (35×35矩阵)
   - 统计dlthistory_allout中蓝球号码(1-12)的跟随次数 → dlt_bluefollowtime (12×12矩阵)
   - 统计dlthistory_allout中蓝球号码(1-12)的跟随概率 → dlt_bluefollowprob (12×12矩阵)
7. 保存数据到Excel文件：
   - 将8个跟随性矩阵数据保存到根目录下的test.xlsx文件中
   - 将8个统计变量（ssq_redtime、ssq_redprob、ssq_bluetime、ssq_blueprob、dlt_redtime、dlt_redprob、dlt_bluetime、dlt_blueprob）保存到对应名称的工作表中
   - 每个数据保存在对应名称的工作表中
8. 根据最新一期号码预测下一期号码：
   - 将ssq_lastNo的7个数值分别赋值给变量s1至s7
   - 将dlt_lastNo的7个数值分别赋值给变量d1至d7
9. 计算加权概率：
   - 计算ssq_sr1：ssq_redfollowprob第s1行的数据乘以ssq_redprob，再除以ssq_redprob[s1]
   - 计算ssq_sr2：ssq_redfollowprob第s2行的数据乘以ssq_redprob，再除以ssq_redprob[s2]
   - 计算ssq_sr3：ssq_redfollowprob第s3行的数据乘以ssq_redprob，再除以ssq_redprob[s3]
   - 计算ssq_sr4：ssq_redfollowprob第s4行的数据乘以ssq_redprob，再除以ssq_redprob[s4]
   - 计算ssq_sr5：ssq_redfollowprob第s5行的数据乘以ssq_redprob，再除以ssq_redprob[s5]
   - 计算ssq_sr6：ssq_redfollowprob第s6行的数据乘以ssq_redprob，再除以ssq_redprob[s6]
   - 计算ssq_sravg：ssq_sr1至ssq_sr6的算术平均值
   - 计算ssq_sbavg：ssq_bluefollowprob第s7行的数据乘以ssq_blueprob，再除以ssq_blueprob[s7]
   - 计算dlt_sr1：dlt_redfollowprob第d1行的数据乘以dlt_redprob，再除以dlt_redprob[d1]
   - 计算dlt_sr2：dlt_redfollowprob第d2行的数据乘以dlt_redprob，再除以dlt_redprob[d2]
   - 计算dlt_sr3：dlt_redfollowprob第d3行的数据乘以dlt_redprob，再除以dlt_redprob[d3]
   - 计算dlt_sr4：dlt_redfollowprob第d4行的数据乘以dlt_redprob，再除以dlt_redprob[d4]
   - 计算dlt_sr5：dlt_redfollowprob第d5行的数据乘以dlt_redprob，再除以dlt_redprob[d5]
   - 计算dlt_sravg：dlt_sr1至dlt_sr5的算术平均值
   - 计算dlt_sr6：dlt_bluefollowprob第d6行的数据乘以dlt_blueprob，再除以dlt_blueprob[d6]
   - 计算dlt_sr7：dlt_bluefollowprob第d7行的数据乘以dlt_blueprob，再除以dlt_blueprob[d7]
   - 计算dlt_sbavg：dlt_sr6和dlt_sr7的算术平均值
10. 确保所有变量具有一致的数据类型
11. 按第一列（NO列）从小到大排序
12. 自动清除无效数据或空数据行
13. 打印所有变量的行数、列数、数据类型和前10行数据
14. 打印最新一期号码
15. 打印号码出现次数与概率统计
16. 打印号码跟随性统计
17. 打印预测的下一期号码
18. 打印加权概率计算结果
19. 筛选并打印预测号码：
   - 筛选双色球红球概率最大的6个号码
   - 筛选双色球蓝球概率最大的1个号码
   - 筛选大乐透红球概率最大的5个号码
   - 筛选大乐透蓝球概率最大的2个号码
"""

import pandas as pd
import numpy as np

def read_lottery_data(file_path):
    """
    读取彩票数据Excel文件，提取特定列数据并保存为变量

    Args:
        file_path: Excel文件路径

    Returns:
        ssqhistory_redramout: 双色球历史红球和蓝球数据
        ssqhistory_allout: 双色球历史所有输出数据
        dlthistory_redramout: 大乐透历史红球和蓝球数据
        dlthistory_allout: 大乐透历史所有输出数据
        ssq_lastNo: 双色球最新一期号码（第2列至第8列）
        dlt_lastNo: 大乐透最新一期号码（第2列至第8列）
        ssq_redtime: 双色球红球出现次数统计
        ssq_redprob: 双色球红球出现概率统计
        ssq_bluetime: 双色球蓝球出现次数统计
        ssq_blueprob: 双色球蓝球出现概率统计
        dlt_redtime: 大乐透红球出现次数统计
        dlt_redprob: 大乐透红球出现概率统计
        dlt_bluetime: 大乐透蓝球出现次数统计
        dlt_blueprob: 大乐透蓝球出现概率统计
        ssq_redfollowtime: 双色球红球跟随次数矩阵
        ssq_redfollowprob: 双色球红球跟随概率矩阵
        ssq_bluefollowtime: 双色球蓝球跟随次数矩阵
        ssq_bluefollowprob: 双色球蓝球跟随概率矩阵
        dlt_redfollowtime: 大乐透红球跟随次数矩阵
        dlt_redfollowprob: 大乐透红球跟随概率矩阵
        dlt_bluefollowtime: 大乐透蓝球跟随次数矩阵
        dlt_bluefollowprob: 大乐透蓝球跟随概率矩阵
    """
    # 读取Excel文件中的双色球数据
    print("正在读取双色球数据...")
    ssq_data = pd.read_excel(file_path, sheet_name="SSQ_data_all", usecols=range(15))  # A-O列 (0-14)

    # 提取双色球红球和蓝球数据 (A列, C-H列)
    # A列是第0列，C-H列是第2-7列
    # 使用copy()创建数据的副本，避免SettingWithCopyWarning
    ssqhistory_redramout = ssq_data.iloc[:, [0] + list(range(2, 8))].copy()

    # 提取双色球所有输出数据 (A列, I-O列)
    # A列是第0列，I-O列是第8-14列
    ssqhistory_allout = ssq_data.iloc[:, [0] + list(range(8, 15))].copy()

    # 读取Excel文件中的大乐透数据
    print("正在读取大乐透数据...")
    dlt_data = pd.read_excel(file_path, sheet_name="DLT_data_all", usecols=range(14))  # A-N列 (0-13)

    # 提取大乐透红球和蓝球数据 (A列, C-G列)
    # A列是第0列，C-G列是第2-6列
    dlthistory_redramout = dlt_data.iloc[:, [0] + list(range(2, 7))].copy()

    # 提取大乐透所有输出数据 (A列, H-N列)
    # A列是第0列，H-N列是第7-13列
    dlthistory_allout = dlt_data.iloc[:, [0] + list(range(7, 14))].copy()

    # 清理和排序数据
    datasets = {
        "ssqhistory_redramout": ssqhistory_redramout,
        "ssqhistory_allout": ssqhistory_allout,
        "dlthistory_redramout": dlthistory_redramout,
        "dlthistory_allout": dlthistory_allout
    }

    for name, dataset in datasets.items():
        # 删除包含NaN的行
        dataset.dropna(inplace=True)

        # 按第一列（NO列）排序
        dataset.sort_values(by=dataset.columns[0], inplace=True)

        # 重置索引
        dataset.reset_index(drop=True, inplace=True)

        # 确保数据类型一致性
        try:
            # 第一列通常是期号，应该是整数类型
            dataset.iloc[:, 0] = dataset.iloc[:, 0].astype(int)

            # 其他列通常是彩票号码，也应该是整数类型
            for col in range(1, dataset.shape[1]):
                dataset.iloc[:, col] = dataset.iloc[:, col].astype(int)
        except ValueError as e:
            print(f"警告: 转换 {name} 的数据类型时出错: {e}")
            print("尝试使用更安全的转换方法...")

            # 使用更安全的方法：先填充NaN，然后转换为整数
            for col in range(dataset.shape[1]):
                # 填充NaN值为0
                dataset.iloc[:, col] = dataset.iloc[:, col].fillna(0)
                # 转换为整数
                dataset.iloc[:, col] = dataset.iloc[:, col].astype(int)

    # 特别确保dlthistory_redramout的数据类型为整数类型
    # 因为在测试中发现它可能仍然是浮点类型
    for col in dlthistory_redramout.columns:
        if col != 'NO':  # NO列已经在前面处理过了
            dlthistory_redramout[col] = dlthistory_redramout[col].astype(int)

    # 提取最新一期号码
    print("提取最新一期号码...")

    # 提取双色球最新一期号码（第2列至第8列，即索引1-7）
    # 使用.iloc[-1, 1:8]获取最后一行的第2列至第8列
    ssq_lastNo = ssqhistory_allout.iloc[-1, 1:8].values

    # 提取大乐透最新一期号码（第2列至第8列，即索引1-7）
    dlt_lastNo = dlthistory_allout.iloc[-1, 1:8].values

    # 确保最新一期号码为整数类型
    ssq_lastNo = ssq_lastNo.astype(int)
    dlt_lastNo = dlt_lastNo.astype(int)

    # 统计号码出现次数和概率
    print("统计号码出现次数和概率...")

    # 1. 统计双色球红球出现次数和概率 (第2列至第7列，即索引1-6)
    # 红球范围：1-33
    ssq_redtime, ssq_redprob = calculate_ball_statistics(
        ssqhistory_allout, range(1, 7), 1, 33)

    # 2. 统计双色球蓝球出现次数和概率 (第8列，即索引7)
    # 蓝球范围：1-16
    ssq_bluetime, ssq_blueprob = calculate_ball_statistics(
        ssqhistory_allout, [7], 1, 16)

    # 3. 统计大乐透红球出现次数和概率 (第2列至第6列，即索引1-5)
    # 红球范围：1-35
    dlt_redtime, dlt_redprob = calculate_ball_statistics(
        dlthistory_allout, range(1, 6), 1, 35)

    # 4. 统计大乐透蓝球出现次数和概率 (第7列至第8列，即索引6-7)
    # 蓝球范围：1-12
    dlt_bluetime, dlt_blueprob = calculate_ball_statistics(
        dlthistory_allout, range(6, 8), 1, 12)

    # 统计号码跟随性
    print("统计号码跟随性...")

    # 1. 统计双色球红球跟随性 (第2列至第7列，即索引1-6)
    # 红球范围：1-33
    print("  计算双色球红球跟随性...")
    ssq_redfollowtime, ssq_redfollowprob = calculate_follow_statistics(
        ssqhistory_allout, range(1, 7), 1, 33)

    # 2. 统计双色球蓝球跟随性 (第8列，即索引7)
    # 蓝球范围：1-16
    print("  计算双色球蓝球跟随性...")
    ssq_bluefollowtime, ssq_bluefollowprob = calculate_follow_statistics(
        ssqhistory_allout, [7], 1, 16)

    # 3. 统计大乐透红球跟随性 (第2列至第6列，即索引1-5)
    # 红球范围：1-35
    print("  计算大乐透红球跟随性...")
    dlt_redfollowtime, dlt_redfollowprob = calculate_follow_statistics(
        dlthistory_allout, range(1, 6), 1, 35)

    # 4. 统计大乐透蓝球跟随性 (第7列至第8列，即索引6-7)
    # 蓝球范围：1-12
    print("  计算大乐透蓝球跟随性...")
    dlt_bluefollowtime, dlt_bluefollowprob = calculate_follow_statistics(
        dlthistory_allout, range(6, 8), 1, 12)

    return (ssqhistory_redramout, ssqhistory_allout, dlthistory_redramout, dlthistory_allout,
            ssq_lastNo, dlt_lastNo,
            ssq_redtime, ssq_redprob, ssq_bluetime, ssq_blueprob,
            dlt_redtime, dlt_redprob, dlt_bluetime, dlt_blueprob,
            ssq_redfollowtime, ssq_redfollowprob, ssq_bluefollowtime, ssq_bluefollowprob,
            dlt_redfollowtime, dlt_redfollowprob, dlt_bluefollowtime, dlt_bluefollowprob)

def print_dataset_info(name, dataset):
    """
    打印数据集的信息

    Args:
        name: 数据集名称
        dataset: 数据集
    """
    print(f"\n{name} 数据信息:")
    print(f"行数: {dataset.shape[0]}, 列数: {dataset.shape[1]}")

    # 打印数据类型信息
    print("数据类型信息:")
    for col in dataset.columns:
        print(f"  {col}: {dataset[col].dtype}")

    # print(f"前10行数据:")
    # print(dataset.head(10))

def print_last_numbers(name, numbers):
    """
    打印最新一期号码信息

    Args:
        name: 彩票名称
        numbers: 号码数组
    """
    print(f"\n{name}最新一期号码:")
    print(f"数据类型: {numbers.dtype}")
    print(f"号码: {numbers}")

def calculate_ball_statistics(df, columns, min_ball, max_ball):
    """
    统计球号出现次数和概率

    Args:
        df: 数据集
        columns: 要统计的列索引列表
        min_ball: 最小球号
        max_ball: 最大球号

    Returns:
        times: 各球号出现次数的字典
        probs: 各球号出现概率的字典
    """
    # 创建一个字典，用于存储每个球号的出现次数
    times = {i: 0 for i in range(min_ball, max_ball + 1)}

    # 将所有指定列的数据合并为一个numpy数组
    all_balls = np.array([])
    for col in columns:
        all_balls = np.append(all_balls, df.iloc[:, col].values)

    # 统计每个球号的出现次数
    for ball in all_balls:
        if min_ball <= ball <= max_ball:
            times[int(ball)] += 1

    # 计算总次数
    total_count = sum(times.values())

    # 计算每个球号的出现概率
    probs = {ball: count / total_count for ball, count in times.items()}

    return times, probs

def print_ball_statistics(name, times, probs, ball_type=""):
    """
    打印球号统计信息

    Args:
        name: 变量名称
        times: 各球号出现次数的字典
        probs: 各球号出现概率的字典
        ball_type: 球的类型，用于打印信息
    """
    print(f"\n{name} {ball_type}统计:")
    print(f"球号\t出现次数\t出现概率")
    for ball in sorted(times.keys()):
        print(f"{ball}\t{times[ball]}\t{probs[ball]:.6f} ({probs[ball]*100:.2f}%)")

def calculate_follow_statistics(df, columns, min_ball, max_ball):
    """
    计算球号的跟随性统计

    Args:
        df: 数据集
        columns: 要统计的列索引列表
        min_ball: 最小球号
        max_ball: 最大球号

    Returns:
        follow_time: 跟随次数矩阵
        follow_prob: 跟随概率矩阵
    """
    # 创建跟随次数矩阵和概率矩阵
    num_balls = max_ball - min_ball + 1
    follow_time = np.zeros((num_balls, num_balls), dtype=int)
    follow_prob = np.zeros((num_balls, num_balls), dtype=float)

    # 遍历数据集中的每一行（除了最后一行）
    for i in range(len(df) - 1):
        # 获取当前行和下一行的球号
        current_row_balls = set()
        next_row_balls = set()

        # 收集当前行的所有球号
        for col in columns:
            ball = df.iloc[i, col]
            if min_ball <= ball <= max_ball:
                current_row_balls.add(int(ball))

        # 收集下一行的所有球号
        for col in columns:
            ball = df.iloc[i+1, col]
            if min_ball <= ball <= max_ball:
                next_row_balls.add(int(ball))

        # 更新跟随次数矩阵
        for current_ball in current_row_balls:
            for next_ball in next_row_balls:
                # 矩阵索引从0开始，所以需要减去min_ball
                follow_time[next_ball - min_ball, current_ball - min_ball] += 1

    # 计算跟随概率矩阵
    for col in range(num_balls):
        col_sum = np.sum(follow_time[:, col])
        if col_sum > 0:
            follow_prob[:, col] = follow_time[:, col] / col_sum

    return follow_time, follow_prob

def print_follow_statistics(name, follow_time, follow_prob, min_ball, ball_type=""):
    """
    打印球号跟随统计信息

    Args:
        name: 变量名称
        follow_time: 跟随次数矩阵
        follow_prob: 跟随概率矩阵
        min_ball: 最小球号
        ball_type: 球的类型，用于打印信息
    """
    print(f"\n{name} {ball_type}跟随统计:")
    print(f"矩阵大小: {follow_time.shape}")
    print(f"示例数据 (前5行5列):")

    # 打印矩阵的前5行5列（或更少，如果矩阵更小）
    rows = min(5, follow_time.shape[0])
    cols = min(5, follow_time.shape[1])

    # 打印列标题
    header = "\t"
    for j in range(cols):
        header += f"{j + min_ball}\t"
    print(header)

    # 打印行数据
    for i in range(rows):
        row_str = f"{i + min_ball}\t"
        for j in range(cols):
            row_str += f"{follow_time[i, j]} ({follow_prob[i, j]:.2f})\t"
        print(row_str)

def save_matrices_to_excel(file_path, matrices_dict, min_balls_dict, statistics_dict=None):
    """
    将矩阵数据和统计数据保存到Excel文件中

    Args:
        file_path: Excel文件路径
        matrices_dict: 包含矩阵数据的字典，键为工作表名称，值为矩阵
        min_balls_dict: 包含每个矩阵最小球号的字典，键为工作表名称，值为最小球号
        statistics_dict: 包含统计数据的字典，键为工作表名称，值为统计字典
    """
    try:
        # 创建一个ExcelWriter对象
        with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
            # 遍历所有矩阵
            for sheet_name, matrix in matrices_dict.items():
                # 获取最小球号
                min_ball = min_balls_dict.get(sheet_name, 1)

                # 创建一个DataFrame，添加行列索引
                num_rows, num_cols = matrix.shape

                # 创建行列标签（球号）
                row_labels = [i + min_ball for i in range(num_rows)]
                col_labels = [i + min_ball for i in range(num_cols)]

                # 创建DataFrame
                df = pd.DataFrame(matrix, index=row_labels, columns=col_labels)

                # 将DataFrame写入Excel
                df.to_excel(writer, sheet_name=sheet_name)

            # 保存统计数据（字典格式）
            if statistics_dict:
                for sheet_name, stats_data in statistics_dict.items():
                    # 将字典转换为DataFrame
                    # 创建两列：球号和对应的值
                    ball_numbers = list(stats_data.keys())
                    values = list(stats_data.values())

                    df = pd.DataFrame({
                        '球号': ball_numbers,
                        '数值': values
                    })

                    # 将DataFrame写入Excel
                    df.to_excel(writer, sheet_name=sheet_name, index=False)

            print(f"\n数据已保存到 {file_path}")
    except Exception as e:
        print(f"保存数据到Excel时出错: {e}")

def main():
    """
    主函数
    """
    file_path = "lottery_data_all.xlsx"

    try:
        print(f"开始读取文件: {file_path}")
        # 读取数据
        (ssqhistory_redramout, ssqhistory_allout, dlthistory_redramout, dlthistory_allout,
         ssq_lastNo, dlt_lastNo,
         ssq_redtime, ssq_redprob, ssq_bluetime, ssq_blueprob,
         dlt_redtime, dlt_redprob, dlt_bluetime, dlt_blueprob,
         ssq_redfollowtime, ssq_redfollowprob, ssq_bluefollowtime, ssq_bluefollowprob,
         dlt_redfollowtime, dlt_redfollowprob, dlt_bluefollowtime, dlt_bluefollowprob) = read_lottery_data(file_path)

        print("\n数据读取完成，所有变量已确保数据类型一致性")
        print("=" * 50)

        # 打印数据信息
        print_dataset_info("ssqhistory_redramout", ssqhistory_redramout)
        print_dataset_info("ssqhistory_allout", ssqhistory_allout)
        print_dataset_info("dlthistory_redramout", dlthistory_redramout)
        print_dataset_info("dlthistory_allout", dlthistory_allout)

        # 验证数据类型一致性
        print("\n验证数据类型一致性:")
        print(f"dlthistory_redramout 与其他变量具有相同的数据类型: {all(dlthistory_redramout[col].dtype == int for col in dlthistory_redramout.columns)}")

        # 打印最新一期号码
        print("\n" + "=" * 50)
        print_last_numbers("双色球", ssq_lastNo)
        print_last_numbers("大乐透", dlt_lastNo)

        # 打印号码统计信息
        print("\n" + "=" * 50)
        print("号码出现次数与概率统计")
        print("=" * 50)

        # 打印双色球红球统计
        print_ball_statistics("ssq_redtime", ssq_redtime, ssq_redprob, "红球")

        # 打印双色球蓝球统计
        print_ball_statistics("ssq_bluetime", ssq_bluetime, ssq_blueprob, "蓝球")

        # 打印大乐透红球统计
        print_ball_statistics("dlt_redtime", dlt_redtime, dlt_redprob, "红球")

        # 打印大乐透蓝球统计
        print_ball_statistics("dlt_bluetime", dlt_bluetime, dlt_blueprob, "蓝球")

        # 打印号码跟随性统计信息
        print("\n" + "=" * 50)
        print("号码跟随性统计")
        print("=" * 50)

        # 打印双色球红球跟随性统计
        print_follow_statistics("ssq_redfollowtime", ssq_redfollowtime, ssq_redfollowprob, 1, "红球")

        # 打印双色球蓝球跟随性统计
        print_follow_statistics("ssq_bluefollowtime", ssq_bluefollowtime, ssq_bluefollowprob, 1, "蓝球")

        # 打印大乐透红球跟随性统计
        print_follow_statistics("dlt_redfollowtime", dlt_redfollowtime, dlt_redfollowprob, 1, "红球")

        # 打印大乐透蓝球跟随性统计
        print_follow_statistics("dlt_bluefollowtime", dlt_bluefollowtime, dlt_bluefollowprob, 1, "蓝球")

        # 保存矩阵数据到Excel文件
        print("\n" + "=" * 50)
        print("保存矩阵数据到Excel文件")
        print("=" * 50)

        # 创建矩阵字典
        matrices_dict = {
            "ssq_redfollowtime": ssq_redfollowtime,
            "ssq_redfollowprob": ssq_redfollowprob,
            "ssq_bluefollowtime": ssq_bluefollowtime,
            "ssq_bluefollowprob": ssq_bluefollowprob,
            "dlt_redfollowtime": dlt_redfollowtime,
            "dlt_redfollowprob": dlt_redfollowprob,
            "dlt_bluefollowtime": dlt_bluefollowtime,
            "dlt_bluefollowprob": dlt_bluefollowprob
        }

        # 创建最小球号字典
        min_balls_dict = {
            "ssq_redfollowtime": 1,
            "ssq_redfollowprob": 1,
            "ssq_bluefollowtime": 1,
            "ssq_bluefollowprob": 1,
            "dlt_redfollowtime": 1,
            "dlt_redfollowprob": 1,
            "dlt_bluefollowtime": 1,
            "dlt_bluefollowprob": 1
        }

        # 创建统计数据字典（包含8个统计变量）
        statistics_dict = {
            "ssq_redtime": ssq_redtime,
            "ssq_redprob": ssq_redprob,
            "ssq_bluetime": ssq_bluetime,
            "ssq_blueprob": ssq_blueprob,
            "dlt_redtime": dlt_redtime,
            "dlt_redprob": dlt_redprob,
            "dlt_bluetime": dlt_bluetime,
            "dlt_blueprob": dlt_blueprob
        }

        # 保存矩阵数据和统计数据到Excel文件
        excel_file = "test.xlsx"
        save_matrices_to_excel(excel_file, matrices_dict, min_balls_dict, statistics_dict)

        # 根据最新一期号码预测下一期号码
        print("\n" + "=" * 50)
        print("根据最新一期号码预测下一期号码")
        print("=" * 50)

        # 1. 将最新一期号码赋值给单独的变量
        # 双色球最新一期号码
        s1, s2, s3, s4, s5, s6, s7 = ssq_lastNo

        # 大乐透最新一期号码
        d1, d2, d3, d4, d5, d6, d7 = dlt_lastNo

        # 打印变量值
        print("\n双色球最新一期号码:")
        print(f"s1 = {s1}, s2 = {s2}, s3 = {s3}, s4 = {s4}, s5 = {s5}, s6 = {s6}, s7 = {s7}")

        print("\n大乐透最新一期号码:")
        print(f"d1 = {d1}, d2 = {d2}, d3 = {d3}, d4 = {d4}, d5 = {d5}, d6 = {d6}, d7 = {d7}")

        # 计算加权概率
        # print("\n" + "=" * 50)
        # print("计算加权概率")
        # print("=" * 50)

        # 将ssq_redprob字典转换为列表，方便按索引访问
        ssq_redprob_list = [ssq_redprob[i] for i in range(1, 34)]

        # 1. 计算s1的加权概率
        # 注意：矩阵索引从0开始，而球号从1开始，所以需要减1
        ssq_sr1 = np.zeros(33)
        for i in range(33):
            ssq_sr1[i] = ssq_redfollowprob[i, s1-1] * ssq_redprob_list[i] / ssq_redprob_list[s1-1]

        # 2. 计算s2的加权概率
        ssq_sr2 = np.zeros(33)
        for i in range(33):
            ssq_sr2[i] = ssq_redfollowprob[i, s2-1] * ssq_redprob_list[i] / ssq_redprob_list[s2-1]

        # 3. 计算s3的加权概率
        ssq_sr3 = np.zeros(33)
        for i in range(33):
            ssq_sr3[i] = ssq_redfollowprob[i, s3-1] * ssq_redprob_list[i] / ssq_redprob_list[s3-1]

        # 4. 计算s4的加权概率
        ssq_sr4 = np.zeros(33)
        for i in range(33):
            ssq_sr4[i] = ssq_redfollowprob[i, s4-1] * ssq_redprob_list[i] / ssq_redprob_list[s4-1]

        # 5. 计算s5的加权概率
        ssq_sr5 = np.zeros(33)
        for i in range(33):
            ssq_sr5[i] = ssq_redfollowprob[i, s5-1] * ssq_redprob_list[i] / ssq_redprob_list[s5-1]

        # 6. 计算s6的加权概率
        ssq_sr6 = np.zeros(33)
        for i in range(33):
            ssq_sr6[i] = ssq_redfollowprob[i, s6-1] * ssq_redprob_list[i] / ssq_redprob_list[s6-1]

        # 7. 计算六个变量的算术平均数
        ssq_sravg = (ssq_sr1 + ssq_sr2 + ssq_sr3 + ssq_sr4 + ssq_sr5 + ssq_sr6) / 6

        # 将ssq_blueprob字典转换为列表，方便按索引访问
        ssq_blueprob_list = [ssq_blueprob[i] for i in range(1, 17)]

        # 8. 计算s7的加权概率
        ssq_sbavg = np.zeros(16)
        for i in range(16):
            ssq_sbavg[i] = ssq_bluefollowprob[i, s7-1] * ssq_blueprob_list[i] / ssq_blueprob_list[s7-1]

        # 将dlt_redprob字典转换为列表，方便按索引访问
        dlt_redprob_list = [dlt_redprob[i] for i in range(1, 36)]

        # 1. 计算d1的加权概率
        # 注意：矩阵索引从0开始，而球号从1开始，所以需要减1
        dlt_sr1 = np.zeros(35)
        for i in range(35):
            dlt_sr1[i] = dlt_redfollowprob[i, d1-1] * dlt_redprob_list[i] / dlt_redprob_list[d1-1]

        # 2. 计算d2的加权概率
        dlt_sr2 = np.zeros(35)
        for i in range(35):
            dlt_sr2[i] = dlt_redfollowprob[i, d2-1] * dlt_redprob_list[i] / dlt_redprob_list[d2-1]

        # 3. 计算d3的加权概率
        dlt_sr3 = np.zeros(35)
        for i in range(35):
            dlt_sr3[i] = dlt_redfollowprob[i, d3-1] * dlt_redprob_list[i] / dlt_redprob_list[d3-1]

        # 4. 计算d4的加权概率
        dlt_sr4 = np.zeros(35)
        for i in range(35):
            dlt_sr4[i] = dlt_redfollowprob[i, d4-1] * dlt_redprob_list[i] / dlt_redprob_list[d4-1]

        # 5. 计算d5的加权概率
        dlt_sr5 = np.zeros(35)
        for i in range(35):
            dlt_sr5[i] = dlt_redfollowprob[i, d5-1] * dlt_redprob_list[i] / dlt_redprob_list[d5-1]

        # 6. 计算五个变量的算术平均数
        dlt_sravg = (dlt_sr1 + dlt_sr2 + dlt_sr3 + dlt_sr4 + dlt_sr5) / 5

        # 将dlt_blueprob字典转换为列表，方便按索引访问
        dlt_blueprob_list = [dlt_blueprob[i] for i in range(1, 13)]

        # 7. 计算d6的加权概率
        dlt_sr6 = np.zeros(12)
        for i in range(12):
            dlt_sr6[i] = dlt_bluefollowprob[i, d6-1] * dlt_blueprob_list[i] / dlt_blueprob_list[d6-1]

        # 8. 计算d7的加权概率
        dlt_sr7 = np.zeros(12)
        for i in range(12):
            dlt_sr7[i] = dlt_bluefollowprob[i, d7-1] * dlt_blueprob_list[i] / dlt_blueprob_list[d7-1]

        # 9. 计算两个变量的算术平均数
        dlt_sbavg = (dlt_sr6 + dlt_sr7) / 2

        # 打印结果
        # print("\n双色球红球加权概率平均值:")
        # for i in range(33):
        #    print(f"球号 {i+1}: {ssq_sravg[i]:.6f}")

        # print("\n双色球蓝球加权概率:")
        # for i in range(16):
        #     print(f"球号 {i+1}: {ssq_sbavg[i]:.6f}")

        # print("\n大乐透红球加权概率平均值:")
        # for i in range(35):
        #    print(f"球号 {i+1}: {dlt_sravg[i]:.6f}")

        # print("\n大乐透蓝球加权概率平均值:")
        # for i in range(12):
        #    print(f"球号 {i+1}: {dlt_sbavg[i]:.6f}")

        # 计算多条件贝叶斯概率
        # print("\n" + "=" * 50)
        # print("计算多条件贝叶斯概率")
        # print("=" * 50)

        # 1. 计算ssq_trueredprob1
        # 将ssq_redfollowprob中第1至33行的第s1、s2、s3、s4、s5与s6列的数据分别乘以ssq_redprob中对应数字的数据，然后求和
        ssq_trueredprob1 = np.zeros(33)
        for i in range(33):  # 第1至33行（索引0-32）
            # 计算第i+1个球号的多条件贝叶斯概率
            prob_sum = 0
            prob_sum += ssq_redfollowprob[i, s1-1] * ssq_redprob_list[s1-1]  # 第s1列数据乘以ssq_redprob中第s1个数据
            prob_sum += ssq_redfollowprob[i, s2-1] * ssq_redprob_list[s2-1]  # 第s2列数据乘以ssq_redprob中第s2个数据
            prob_sum += ssq_redfollowprob[i, s3-1] * ssq_redprob_list[s3-1]  # 第s3列数据乘以ssq_redprob中第s3个数据
            prob_sum += ssq_redfollowprob[i, s4-1] * ssq_redprob_list[s4-1]  # 第s4列数据乘以ssq_redprob中第s4个数据
            prob_sum += ssq_redfollowprob[i, s5-1] * ssq_redprob_list[s5-1]  # 第s5列数据乘以ssq_redprob中第s5个数据
            prob_sum += ssq_redfollowprob[i, s6-1] * ssq_redprob_list[s6-1]  # 第s6列数据乘以ssq_redprob中第s6个数据
            ssq_trueredprob1[i] = prob_sum

        # 2. 计算ssq_trueblueprob1
        # 将ssq_bluefollowprob中第1至16行的第s7列的数据乘以ssq_blueprob中第s7数字对应的数据
        ssq_trueblueprob1 = np.zeros(16)
        for i in range(16):  # 第1至16行（索引0-15）
            # 计算第i+1个球号的多条件贝叶斯概率
            ssq_trueblueprob1[i] = ssq_bluefollowprob[i, s7-1] * ssq_blueprob_list[s7-1]

        # 3. 计算dlt_trueredprob1
        # 将dlt_redfollowprob中第1至35行的第d1、d2、d3、d4与d5列的数据分别乘以dlt_redprob中对应数字的数据，然后求和
        dlt_trueredprob1 = np.zeros(35)
        for i in range(35):  # 第1至35行（索引0-34）
            # 计算第i+1个球号的多条件贝叶斯概率
            prob_sum = 0
            prob_sum += dlt_redfollowprob[i, d1-1] * dlt_redprob_list[d1-1]  # 第d1列数据乘以dlt_redprob中第d1个数据
            prob_sum += dlt_redfollowprob[i, d2-1] * dlt_redprob_list[d2-1]  # 第d2列数据乘以dlt_redprob中第d2个数据
            prob_sum += dlt_redfollowprob[i, d3-1] * dlt_redprob_list[d3-1]  # 第d3列数据乘以dlt_redprob中第d3个数据
            prob_sum += dlt_redfollowprob[i, d4-1] * dlt_redprob_list[d4-1]  # 第d4列数据乘以dlt_redprob中第d4个数据
            prob_sum += dlt_redfollowprob[i, d5-1] * dlt_redprob_list[d5-1]  # 第d5列数据乘以dlt_redprob中第d5个数据
            dlt_trueredprob1[i] = prob_sum

        # 4. 计算dlt_trueblueprob1
        # 将dlt_bluefollowprob中第1至12行的第d6与d7列的数据分别乘以dlt_blueprob中对应数字的数据，然后求和
        dlt_trueblueprob1 = np.zeros(12)
        for i in range(12):  # 第1至12行（索引0-11）
            # 计算第i+1个球号的多条件贝叶斯概率
            prob_sum = 0
            prob_sum += dlt_bluefollowprob[i, d6-1] * dlt_blueprob_list[d6-1]  # 第d6列数据乘以dlt_blueprob中第d6个数据
            prob_sum += dlt_bluefollowprob[i, d7-1] * dlt_blueprob_list[d7-1]  # 第d7列数据乘以dlt_blueprob中第d7个数据
            dlt_trueblueprob1[i] = prob_sum

        # 打印多条件贝叶斯概率结果
        # print("\n双色球红球多条件贝叶斯概率:")
        # for i in range(33):
        #     print(f"球号 {i+1}: {ssq_trueredprob1[i]:.6f}")

        # print("\n双色球蓝球多条件贝叶斯概率:")
        # for i in range(16):
        #     print(f"球号 {i+1}: {ssq_trueblueprob1[i]:.6f}")

        # print("\n大乐透红球多条件贝叶斯概率:")
        # for i in range(35):
        #     print(f"球号 {i+1}: {dlt_trueredprob1[i]:.6f}")

        # print("\n大乐透蓝球多条件贝叶斯概率:")
        # for i in range(12):
        #    print(f"球号 {i+1}: {dlt_trueblueprob1[i]:.6f}")

        # 计算全条件贝叶斯概率
        # print("\n" + "=" * 50)
        # print("计算全条件贝叶斯概率")
        # print("=" * 50)

        # 1. 计算ssq_trueredprob2
        # 将ssq_redfollowprob中第1至33行的所有列的数据分别依次乘以ssq_redprob中第1至33个数据，然后求和
        ssq_trueredprob2 = np.zeros(33)
        for i in range(33):  # 第1至33行（索引0-32）
            # 计算第i+1个球号的全条件贝叶斯概率
            prob_sum = 0
            for j in range(33):  # 所有列（索引0-32）
                prob_sum += ssq_redfollowprob[i, j] * ssq_redprob_list[j]  # 第j+1列数据乘以ssq_redprob中第j+1个数据
            ssq_trueredprob2[i] = prob_sum

        # 2. 计算ssq_trueblueprob2
        # 将ssq_bluefollowprob中第1至16行的所有列的数据分别依次乘以ssq_blueprob中第1至16个数据，然后求和
        ssq_trueblueprob2 = np.zeros(16)
        for i in range(16):  # 第1至16行（索引0-15）
            # 计算第i+1个球号的全条件贝叶斯概率
            prob_sum = 0
            for j in range(16):  # 所有列（索引0-15）
                prob_sum += ssq_bluefollowprob[i, j] * ssq_blueprob_list[j]  # 第j+1列数据乘以ssq_blueprob中第j+1个数据
            ssq_trueblueprob2[i] = prob_sum

        # 3. 计算dlt_trueredprob2
        # 将dlt_redfollowprob中第1至35行的所有列的数据分别依次乘以dlt_redprob中第1至35个数据，然后求和
        dlt_trueredprob2 = np.zeros(35)
        for i in range(35):  # 第1至35行（索引0-34）
            # 计算第i+1个球号的全条件贝叶斯概率
            prob_sum = 0
            for j in range(35):  # 所有列（索引0-34）
                prob_sum += dlt_redfollowprob[i, j] * dlt_redprob_list[j]  # 第j+1列数据乘以dlt_redprob中第j+1个数据
            dlt_trueredprob2[i] = prob_sum

        # 4. 计算dlt_trueblueprob2
        # 将dlt_bluefollowprob中第1至12行的所有列的数据分别依次乘以dlt_blueprob中第1至12个数据，然后求和
        dlt_trueblueprob2 = np.zeros(12)
        for i in range(12):  # 第1至12行（索引0-11）
            # 计算第i+1个球号的全条件贝叶斯概率
            prob_sum = 0
            for j in range(12):  # 所有列（索引0-11）
                prob_sum += dlt_bluefollowprob[i, j] * dlt_blueprob_list[j]  # 第j+1列数据乘以dlt_blueprob中第j+1个数据
            dlt_trueblueprob2[i] = prob_sum

        # 打印全条件贝叶斯概率结果
        # print("\n双色球红球全条件贝叶斯概率:")
        # for i in range(33):
        #    print(f"球号 {i+1}: {ssq_trueredprob2[i]:.6f}")

        # print("\n双色球蓝球全条件贝叶斯概率:")
        # for i in range(16):
        #     print(f"球号 {i+1}: {ssq_trueblueprob2[i]:.6f}")

        # print("\n大乐透红球全条件贝叶斯概率:")
        # for i in range(35):
        #    print(f"球号 {i+1}: {dlt_trueredprob2[i]:.6f}")

        # print("\n大乐透蓝球全条件贝叶斯概率:")
        # for i in range(12):
        #     print(f"球号 {i+1}: {dlt_trueblueprob2[i]:.6f}")

        # 筛选号码
        print("\n" + "=" * 50)
        print("筛选号码")
        print("=" * 50)

        # 1. 打印最新一期双色球号码
        print("\n最新一期双色球号码:")
        print(f"ssq_lastNo = {ssq_lastNo}")

        # 2. 打印最新一期大乐透号码
        print("\n最新一期大乐透号码:")
        print(f"dlt_lastNo = {dlt_lastNo}")

        # 3. 筛选双色球红球概率最大的6个号码
        # 创建一个包含号码和概率的列表
        ssq_red_probs = [(i+1, ssq_sravg[i]) for i in range(33)]
        # 按概率从大到小排序
        ssq_red_probs.sort(key=lambda x: x[1], reverse=True)
        # 取前6个号码
        ssq_red_top6 = [num for num, _ in ssq_red_probs[:6]]
        # 按号码从小到大排序
        ssq_red_top6.sort()
        # 打印结果
        print(f"\n基于贝叶斯概率预测的下一期双色球的红球号码为：{ssq_red_top6}")

        # 4. 筛选双色球蓝球概率最大的1个号码
        # 创建一个包含号码和概率的列表
        ssq_blue_probs = [(i+1, ssq_sbavg[i]) for i in range(16)]
        # 按概率从大到小排序
        ssq_blue_probs.sort(key=lambda x: x[1], reverse=True)
        # 取前1个号码
        ssq_blue_top1 = [num for num, _ in ssq_blue_probs[:1]]
        # 打印结果
        print(f"基于贝叶斯概率预测的下一期双色球的蓝球号码为：{ssq_blue_top1}")

        # 5. 筛选大乐透红球概率最大的5个号码
        # 创建一个包含号码和概率的列表
        dlt_red_probs = [(i+1, dlt_sravg[i]) for i in range(35)]
        # 按概率从大到小排序
        dlt_red_probs.sort(key=lambda x: x[1], reverse=True)
        # 取前5个号码
        dlt_red_top5 = [num for num, _ in dlt_red_probs[:5]]
        # 按号码从小到大排序
        dlt_red_top5.sort()
        # 打印结果
        print(f"\n基于贝叶斯概率预测的下一期大乐透的前区号码为：{dlt_red_top5}")

        # 6. 筛选大乐透蓝球概率最大的2个号码
        # 创建一个包含号码和概率的列表
        dlt_blue_probs = [(i+1, dlt_sbavg[i]) for i in range(12)]
        # 按概率从大到小排序
        dlt_blue_probs.sort(key=lambda x: x[1], reverse=True)
        # 取前2个号码
        dlt_blue_top2 = [num for num, _ in dlt_blue_probs[:2]]
        # 按号码从小到大排序
        dlt_blue_top2.sort()
        # 打印结果
        print(f"基于贝叶斯概率预测的下一期大乐透的后区号码为：{dlt_blue_top2}")

        # 基于多条件贝叶斯概率的预测
        print("\n" + "=" * 50)
        print("基于多条件贝叶斯概率的预测")
        print("=" * 50)

        # 5. 基于ssq_trueredprob1筛选数值最大的6个数据，并反馈对应的编号
        # 创建一个包含号码和概率的列表
        ssq_true_red_probs = [(i+1, ssq_trueredprob1[i]) for i in range(33)]
        # 按概率从大到小排序
        ssq_true_red_probs.sort(key=lambda x: x[1], reverse=True)
        # 取前6个号码
        ssq_true_red_top6 = [num for num, _ in ssq_true_red_probs[:6]]
        # 按号码从小到大排序
        ssq_true_red_top6.sort()
        # 打印结果
        print(f"基于多条件贝叶斯概率预测的下一期双色球的红球号码为：{ssq_true_red_top6}")

        # 6. 基于ssq_trueblueprob1筛选数值最大的1个数据，并反馈对应的编号
        # 创建一个包含号码和概率的列表
        ssq_true_blue_probs = [(i+1, ssq_trueblueprob1[i]) for i in range(16)]
        # 按概率从大到小排序
        ssq_true_blue_probs.sort(key=lambda x: x[1], reverse=True)
        # 取前1个号码
        ssq_true_blue_top1 = [num for num, _ in ssq_true_blue_probs[:1]]
        # 打印结果
        print(f"基于多条件贝叶斯概率预测的下一期双色球的蓝球号码为：{ssq_true_blue_top1}")

        # 7. 基于dlt_trueredprob1筛选数值最大的5个数据，并反馈对应的编号
        # 创建一个包含号码和概率的列表
        dlt_true_red_probs = [(i+1, dlt_trueredprob1[i]) for i in range(35)]
        # 按概率从大到小排序
        dlt_true_red_probs.sort(key=lambda x: x[1], reverse=True)
        # 取前5个号码
        dlt_true_red_top5 = [num for num, _ in dlt_true_red_probs[:5]]
        # 按号码从小到大排序
        dlt_true_red_top5.sort()
        # 打印结果
        print(f"基于多条件贝叶斯概率预测的下一期大乐透的前区号码为：{dlt_true_red_top5}")

        # 8. 基于dlt_trueblueprob1筛选概率最大的2个数据，并反馈对应的编号
        # 创建一个包含号码和概率的列表
        dlt_true_blue_probs = [(i+1, dlt_trueblueprob1[i]) for i in range(12)]
        # 按概率从大到小排序
        dlt_true_blue_probs.sort(key=lambda x: x[1], reverse=True)
        # 取前2个号码
        dlt_true_blue_top2 = [num for num, _ in dlt_true_blue_probs[:2]]
        # 按号码从小到大排序
        dlt_true_blue_top2.sort()
        # 打印结果
        print(f"基于多条件贝叶斯概率预测的下一期大乐透的后区号码为：{dlt_true_blue_top2}")

        # 基于全条件贝叶斯概率的预测
        print("\n" + "=" * 50)
        print("基于全条件贝叶斯概率的预测")
        print("=" * 50)

        # 5. 基于ssq_trueredprob2筛选数值最大的6个数据，并反馈对应的编号
        # 创建一个包含号码和概率的列表
        ssq_true_red_probs2 = [(i+1, ssq_trueredprob2[i]) for i in range(33)]
        # 按概率从大到小排序
        ssq_true_red_probs2.sort(key=lambda x: x[1], reverse=True)
        # 取前6个号码
        ssq_true_red_top6_2 = [num for num, _ in ssq_true_red_probs2[:6]]
        # 按号码从小到大排序
        ssq_true_red_top6_2.sort()
        # 打印结果
        print(f"基于全条件贝叶斯概率预测的下一期双色球的红球号码为：{ssq_true_red_top6_2}")

        # 6. 基于ssq_trueblueprob2筛选数值最大的1个数据，并反馈对应的编号
        # 创建一个包含号码和概率的列表
        ssq_true_blue_probs2 = [(i+1, ssq_trueblueprob2[i]) for i in range(16)]
        # 按概率从大到小排序
        ssq_true_blue_probs2.sort(key=lambda x: x[1], reverse=True)
        # 取前1个号码
        ssq_true_blue_top1_2 = [num for num, _ in ssq_true_blue_probs2[:1]]
        # 打印结果
        print(f"基于全条件贝叶斯概率预测的下一期双色球的蓝球号码为：{ssq_true_blue_top1_2}")

        # 7. 基于dlt_trueredprob2筛选数值最大的5个数据，并反馈对应的编号
        # 创建一个包含号码和概率的列表
        dlt_true_red_probs2 = [(i+1, dlt_trueredprob2[i]) for i in range(35)]
        # 按概率从大到小排序
        dlt_true_red_probs2.sort(key=lambda x: x[1], reverse=True)
        # 取前5个号码
        dlt_true_red_top5_2 = [num for num, _ in dlt_true_red_probs2[:5]]
        # 按号码从小到大排序
        dlt_true_red_top5_2.sort()
        # 打印结果
        print(f"基于全条件贝叶斯概率预测的下一期大乐透的前区号码为：{dlt_true_red_top5_2}")

        # 8. 基于dlt_trueblueprob2筛选概率最大的2个数据，并反馈对应的编号
        # 创建一个包含号码和概率的列表
        dlt_true_blue_probs2 = [(i+1, dlt_trueblueprob2[i]) for i in range(12)]
        # 按概率从大到小排序
        dlt_true_blue_probs2.sort(key=lambda x: x[1], reverse=True)
        # 取前2个号码
        dlt_true_blue_top2_2 = [num for num, _ in dlt_true_blue_probs2[:2]]
        # 按号码从小到大排序
        dlt_true_blue_top2_2.sort()
        # 打印结果
        print(f"基于全条件贝叶斯概率预测的下一期大乐透的后区号码为：{dlt_true_blue_top2_2}")

    except FileNotFoundError:
        print(f"错误: 找不到文件 '{file_path}'，请确保文件存在于程序根目录下。")
    except Exception as e:
        print(f"错误: {str(e)}")

if __name__ == "__main__":
    main()
