#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
彩票数据分析三步程序
按照用户要求的三个步骤进行彩票数据分析：

第一步：读取Excel表格数据并排序
- 读取根目录下的"lottery_data_all.xlsx"文件
- 从"SSQ_data_all"标签页提取A列、I列至O列数据（共8列），保存为ssqhistory_allout变量
  第1列数据为期号，第2至第7列数据为每期红球号码，第8列为每期篮球号码
- 从"DLT_data_all"标签页提取A列、H列至N列数据（共8列），保存为dlthistory_allout变量
  第1列数据为期号，第2至第6列数据为每期红球号码，第7与第8列为每期篮球号码
- 按第一列（NO列）序号从小到大排列
- 自动清空无效数据或空数据行

第二步：指定数据
- 程序询问用户想选择1.SSQ还是2.DLT
- 如果用户选择1，则基于ssqhistory_allout进行后续计算与分析
- 如果用户选择2，则基于dlthistory_allout进行后续计算与分析
- 如果用户不选择，等待30s后，默认选择1.SSQ

第三步：开始统计分析
- 基于全部历史数据，运用数学概率统计相关知识和AI大模型的能力
- 找出潜在的和可能的趋势规律，来预测SSQ或DLT下一期的红球和蓝球号码
- 打印相关运算的依据，包括：
  1. 号码出现频率统计
  2. 号码模式分析（奇偶比例、大小比例、连号情况）
  3. 趋势分析（最近20期的热号和冷号）
  4. 基于贝叶斯概率的预测结果
"""

import pandas as pd
from collections import Counter

class LotteryPredictor:
    """彩票预测器类"""
    
    def __init__(self, file_path="lottery_data_all.xlsx"):
        """
        初始化彩票预测器
        
        Args:
            file_path: Excel文件路径
        """
        self.file_path = file_path
        self.ssqhistory_allout = None
        self.dlthistory_allout = None
        self.selected_data = None
        self.lottery_type = None
        
    def step1_read_and_sort_data(self):
        """
        第一步：读取Excel表格数据并排序
        """
        print("=" * 60)
        print("第一步：读取Excel表格数据并排序")
        print("=" * 60)
        
        try:
            # 读取双色球数据
            print("正在读取双色球数据...")
            ssq_data = pd.read_excel(self.file_path, sheet_name="SSQ_data_all")
            
            # 提取A列、I列至O列数据（共8列）
            # A列是第0列，I列至O列是第8-14列
            # 第1列数据为期号，第2至第7列数据为每期红球号码，第8列为每期篮球号码
            self.ssqhistory_allout = ssq_data.iloc[:, [0] + list(range(8, 15))].copy()
            
            # 读取大乐透数据
            print("正在读取大乐透数据...")
            dlt_data = pd.read_excel(self.file_path, sheet_name="DLT_data_all")
            
            # 提取A列、H列至N列数据（共8列）
            # A列是第0列，H列至N列是第7-13列
            # 第1列数据为期号，第2至第6列数据为每期红球号码，第7与第8列为每期篮球号码
            self.dlthistory_allout = dlt_data.iloc[:, [0] + list(range(7, 14))].copy()
            
            # 清理和排序数据
            datasets = {
                "ssqhistory_allout": self.ssqhistory_allout,
                "dlthistory_allout": self.dlthistory_allout
            }
            
            for name, dataset in datasets.items():
                print(f"处理 {name} 数据...")
                
                # 删除包含NaN的行（自动清空无效数据或空数据行）
                dataset.dropna(inplace=True)
                
                # 按第一列（NO列）从小到大排序
                dataset.sort_values(by=dataset.columns[0], inplace=True)
                
                # 重置索引
                dataset.reset_index(drop=True, inplace=True)
                
                # 确保数据类型一致性
                try:
                    # 第一列是期号，应该是整数类型
                    dataset.iloc[:, 0] = dataset.iloc[:, 0].astype(int)
                    
                    # 其他列是彩票号码，也应该是整数类型
                    for col in range(1, dataset.shape[1]):
                        dataset.iloc[:, col] = dataset.iloc[:, col].astype(int)
                except ValueError as e:
                    print(f"警告: 转换 {name} 的数据类型时出错: {e}")
                    # 使用更安全的方法
                    for col in range(dataset.shape[1]):
                        dataset.iloc[:, col] = dataset.iloc[:, col].fillna(0).astype(int)
            
            # 打印数据信息
            print(f"\nssqhistory_allout 数据信息:")
            print(f"行数: {self.ssqhistory_allout.shape[0]}, 列数: {self.ssqhistory_allout.shape[1]}")
            print(f"期号范围: {self.ssqhistory_allout.iloc[0, 0]} - {self.ssqhistory_allout.iloc[-1, 0]}")
            print(f"列名: {list(self.ssqhistory_allout.columns)}")
            
            print(f"\ndlthistory_allout 数据信息:")
            print(f"行数: {self.dlthistory_allout.shape[0]}, 列数: {self.dlthistory_allout.shape[1]}")
            print(f"期号范围: {self.dlthistory_allout.iloc[0, 0]} - {self.dlthistory_allout.iloc[-1, 0]}")
            print(f"列名: {list(self.dlthistory_allout.columns)}")
            
            print("\n第一步完成：数据读取和排序成功！")
            return True
            
        except FileNotFoundError:
            print(f"错误: 找不到文件 '{self.file_path}'，请确保文件存在。")
            return False
        except Exception as e:
            print(f"错误: {str(e)}")
            return False

    def get_user_input_with_timeout(self, prompt, timeout=30, default_value=None):
        """
        获取用户输入，支持超时
        
        Args:
            prompt: 提示信息
            timeout: 超时时间（秒）
            default_value: 默认值
            
        Returns:
            用户输入或默认值
        """
        print(f"{prompt}（{timeout}秒后将使用默认值: {default_value}）")
        
        # 使用简单的方式：直接获取输入，不实现复杂的超时机制
        try:
            user_input = input("请输入（直接回车使用默认值）: ")
            if user_input.strip() == "":
                print(f"使用默认值: {default_value}")
                return str(default_value) if default_value is not None else ""
            return user_input.strip()
        except KeyboardInterrupt:
            print(f"\n用户中断，使用默认值: {default_value}")
            return str(default_value) if default_value is not None else ""

    def step2_specify_data(self):
        """
        第二步：指定数据
        程序询问用户想选择1.SSQ还是2.DLT，如果用户选择1，则表示基于ssqhistory_allout进行后续计算与分析，
        如果用户选择2，则表示基于dlthistory_allout进行后续计算与分析，
        如果用户不选择，等待30s后，默认用户选择了1.SSQ。
        """
        print("\n" + "=" * 60)
        print("第二步：指定数据")
        print("=" * 60)
        
        print("\n请选择彩票类型:")
        print("1. SSQ (双色球)")
        print("2. DLT (大乐透)")
        
        lottery_choice = self.get_user_input_with_timeout(
            "请输入选择 (1 或 2，30秒后默认选择1): ", 30, "1"
        )
        
        if lottery_choice == "2":
            self.lottery_type = "DLT"
            self.selected_data = self.dlthistory_allout
            print("您选择了：大乐透 (DLT)")
            print("将基于dlthistory_allout进行后续计算与分析")
        else:
            self.lottery_type = "SSQ"
            self.selected_data = self.ssqhistory_allout
            print("您选择了：双色球 (SSQ)")
            print("将基于ssqhistory_allout进行后续计算与分析")
        
        print(f"\n选择的数据集信息:")
        print(f"数据行数: {len(self.selected_data)}")
        print(f"数据列数: {self.selected_data.shape[1]}")
        print(f"期号范围: {self.selected_data.iloc[0, 0]} - {self.selected_data.iloc[-1, 0]}")
        
        print("\n第二步完成：数据指定成功！")
        return True

    def calculate_frequency_statistics(self):
        """
        计算号码出现频率统计

        Returns:
            red_freq: 红球频率统计
            blue_freq: 蓝球频率统计
        """
        if self.lottery_type == "SSQ":
            # 双色球：红球1-33，蓝球1-16
            red_columns = range(1, 7)  # 第2-7列是红球
            blue_columns = [7]  # 第8列是蓝球
            red_range = (1, 33)
            blue_range = (1, 16)
        else:  # DLT
            # 大乐透：红球1-35，蓝球1-12
            red_columns = range(1, 6)  # 第2-6列是红球
            blue_columns = [6, 7]  # 第7-8列是蓝球
            red_range = (1, 35)
            blue_range = (1, 12)

        # 统计红球频率
        red_freq = {i: 0 for i in range(red_range[0], red_range[1] + 1)}
        for col in red_columns:
            for value in self.selected_data.iloc[:, col]:
                if red_range[0] <= value <= red_range[1]:
                    red_freq[value] += 1

        # 统计蓝球频率
        blue_freq = {i: 0 for i in range(blue_range[0], blue_range[1] + 1)}
        for col in blue_columns:
            for value in self.selected_data.iloc[:, col]:
                if blue_range[0] <= value <= blue_range[1]:
                    blue_freq[value] += 1

        return red_freq, blue_freq

    def is_prime(self, n):
        """
        判断一个数是否为质数

        Args:
            n: 要判断的数

        Returns:
            bool: 是否为质数
        """
        if n < 2:
            return False
        if n == 2:
            return True
        if n % 2 == 0:
            return False
        for i in range(3, int(n**0.5) + 1, 2):
            if n % i == 0:
                return False
        return True

    def calculate_repeat_analysis(self):
        """
        计算重号分析（连续两期的相同号码）

        Returns:
            repeat_stats: 重号统计结果
        """
        repeat_stats = {
            'red_repeats': [],  # 红球重号记录
            'blue_repeats': [],  # 蓝球重号记录
            'red_repeat_count': 0,  # 红球重号总次数
            'blue_repeat_count': 0,  # 蓝球重号总次数
            'red_repeat_numbers': {},  # 各红球号码的重号次数
            'blue_repeat_numbers': {}  # 各蓝球号码的重号次数
        }

        if self.lottery_type == "SSQ":
            red_columns = range(1, 7)
            blue_columns = [7]
            red_range = (1, 33)
            blue_range = (1, 16)
        else:
            red_columns = range(1, 6)
            blue_columns = [6, 7]
            red_range = (1, 35)
            blue_range = (1, 12)

        # 初始化重号次数统计
        for i in range(red_range[0], red_range[1] + 1):
            repeat_stats['red_repeat_numbers'][i] = 0
        for i in range(blue_range[0], blue_range[1] + 1):
            repeat_stats['blue_repeat_numbers'][i] = 0

        # 分析连续两期的重号情况
        for i in range(len(self.selected_data) - 1):
            # 获取当前期和下一期的号码
            current_red = set([self.selected_data.iloc[i, col] for col in red_columns])
            next_red = set([self.selected_data.iloc[i+1, col] for col in red_columns])

            current_blue = set([self.selected_data.iloc[i, col] for col in blue_columns])
            next_blue = set([self.selected_data.iloc[i+1, col] for col in blue_columns])

            # 计算红球重号
            red_repeat = current_red & next_red
            if red_repeat:
                repeat_stats['red_repeats'].append({
                    'period1': self.selected_data.iloc[i, 0],
                    'period2': self.selected_data.iloc[i+1, 0],
                    'repeat_numbers': list(red_repeat),
                    'repeat_count': len(red_repeat)
                })
                repeat_stats['red_repeat_count'] += len(red_repeat)
                for num in red_repeat:
                    repeat_stats['red_repeat_numbers'][num] += 1

            # 计算蓝球重号
            blue_repeat = current_blue & next_blue
            if blue_repeat:
                repeat_stats['blue_repeats'].append({
                    'period1': self.selected_data.iloc[i, 0],
                    'period2': self.selected_data.iloc[i+1, 0],
                    'repeat_numbers': list(blue_repeat),
                    'repeat_count': len(blue_repeat)
                })
                repeat_stats['blue_repeat_count'] += len(blue_repeat)
                for num in blue_repeat:
                    repeat_stats['blue_repeat_numbers'][num] += 1

        return repeat_stats

    def calculate_pattern_analysis(self):
        """
        计算号码模式分析

        Returns:
            patterns: 模式分析结果
        """
        patterns = {}

        if self.lottery_type == "SSQ":
            red_columns = range(1, 7)
            blue_columns = [7]
        else:
            red_columns = range(1, 6)
            blue_columns = [6, 7]

        # 分析奇偶比例
        odd_even_patterns = []
        for i in range(len(self.selected_data)):
            red_numbers = [self.selected_data.iloc[i, col] for col in red_columns]
            odd_count = sum(1 for num in red_numbers if num % 2 == 1)
            even_count = len(red_numbers) - odd_count
            odd_even_patterns.append(f"{odd_count}:{even_count}")

        patterns['odd_even'] = Counter(odd_even_patterns)

        # 分析大小比例（以中位数为界）
        if self.lottery_type == "SSQ":
            mid_point = 17  # 33的中位数
        else:
            mid_point = 18  # 35的中位数

        big_small_patterns = []
        for i in range(len(self.selected_data)):
            red_numbers = [self.selected_data.iloc[i, col] for col in red_columns]
            big_count = sum(1 for num in red_numbers if num > mid_point)
            small_count = len(red_numbers) - big_count
            big_small_patterns.append(f"{big_count}:{small_count}")

        patterns['big_small'] = Counter(big_small_patterns)

        # 分析连号情况
        consecutive_patterns = []
        for i in range(len(self.selected_data)):
            red_numbers = sorted([self.selected_data.iloc[i, col] for col in red_columns])
            consecutive_count = 0
            for j in range(len(red_numbers) - 1):
                if red_numbers[j+1] - red_numbers[j] == 1:
                    consecutive_count += 1
            consecutive_patterns.append(consecutive_count)

        patterns['consecutive'] = Counter(consecutive_patterns)

        # 分析质合比例（质数vs合数）
        prime_composite_patterns = []
        for i in range(len(self.selected_data)):
            red_numbers = [self.selected_data.iloc[i, col] for col in red_columns]
            prime_count = sum(1 for num in red_numbers if self.is_prime(num))
            composite_count = len(red_numbers) - prime_count
            prime_composite_patterns.append(f"{prime_count}:{composite_count}")

        patterns['prime_composite'] = Counter(prime_composite_patterns)

        return patterns

    def calculate_trend_analysis(self, recent_periods=100):
        """
        计算趋势分析

        Args:
            recent_periods: 分析最近多少期的趋势

        Returns:
            trends: 趋势分析结果
        """
        trends = {}

        if len(self.selected_data) < recent_periods:
            recent_periods = len(self.selected_data)

        recent_data = self.selected_data.tail(recent_periods)

        if self.lottery_type == "SSQ":
            red_columns = range(1, 7)
            blue_columns = [7]
            red_range = (1, 33)
            blue_range = (1, 16)
        else:
            red_columns = range(1, 6)
            blue_columns = [6, 7]
            red_range = (1, 35)
            blue_range = (1, 12)

        # 计算最近期数中各号码的出现次数
        red_recent_freq = {i: 0 for i in range(red_range[0], red_range[1] + 1)}
        for col in red_columns:
            for value in recent_data.iloc[:, col]:
                if red_range[0] <= value <= red_range[1]:
                    red_recent_freq[value] += 1

        blue_recent_freq = {i: 0 for i in range(blue_range[0], blue_range[1] + 1)}
        for col in blue_columns:
            for value in recent_data.iloc[:, col]:
                if blue_range[0] <= value <= blue_range[1]:
                    blue_recent_freq[value] += 1

        trends['red_recent'] = red_recent_freq
        trends['blue_recent'] = blue_recent_freq

        # 计算热号和冷号
        red_hot = sorted(red_recent_freq.items(), key=lambda x: x[1], reverse=True)[:10]
        red_cold = sorted(red_recent_freq.items(), key=lambda x: x[1])[:10]

        blue_hot = sorted(blue_recent_freq.items(), key=lambda x: x[1], reverse=True)[:5]
        blue_cold = sorted(blue_recent_freq.items(), key=lambda x: x[1])[:5]

        trends['red_hot'] = red_hot
        trends['red_cold'] = red_cold
        trends['blue_hot'] = blue_hot
        trends['blue_cold'] = blue_cold

        return trends

    def bayesian_prediction(self, red_freq, blue_freq, trends, patterns):
        """
        基于贝叶斯概率进行预测

        Args:
            red_freq: 红球频率统计
            blue_freq: 蓝球频率统计
            trends: 趋势分析结果
            patterns: 模式分析结果

        Returns:
            prediction: 预测结果
        """
        prediction = {}

        if self.lottery_type == "SSQ":
            red_count = 6
            blue_count = 1
            red_range = (1, 33)
            blue_range = (1, 16)
        else:
            red_count = 5
            blue_count = 2
            red_range = (1, 35)
            blue_range = (1, 12)

        # 计算红球预测概率
        red_probs = {}
        total_red_freq = sum(red_freq.values())

        for ball in range(red_range[0], red_range[1] + 1):
            # 基础概率（历史频率）
            base_prob = red_freq[ball] / total_red_freq if total_red_freq > 0 else 1 / (red_range[1] - red_range[0] + 1)

            # 趋势权重（最近期数的表现）
            trend_weight = trends['red_recent'][ball] / sum(trends['red_recent'].values()) if sum(trends['red_recent'].values()) > 0 else 0

            # 综合概率（基础概率 * 0.7 + 趋势权重 * 0.3）
            red_probs[ball] = base_prob * 0.7 + trend_weight * 0.3

        # 计算蓝球预测概率
        blue_probs = {}
        total_blue_freq = sum(blue_freq.values())

        for ball in range(blue_range[0], blue_range[1] + 1):
            # 基础概率（历史频率）
            base_prob = blue_freq[ball] / total_blue_freq if total_blue_freq > 0 else 1 / (blue_range[1] - blue_range[0] + 1)

            # 趋势权重（最近期数的表现）
            trend_weight = trends['blue_recent'][ball] / sum(trends['blue_recent'].values()) if sum(trends['blue_recent'].values()) > 0 else 0

            # 综合概率（基础概率 * 0.7 + 趋势权重 * 0.3）
            blue_probs[ball] = base_prob * 0.7 + trend_weight * 0.3

        # 选择概率最高的号码
        red_sorted = sorted(red_probs.items(), key=lambda x: x[1], reverse=True)
        blue_sorted = sorted(blue_probs.items(), key=lambda x: x[1], reverse=True)

        predicted_red = [ball for ball, _ in red_sorted[:red_count]]
        predicted_blue = [ball for ball, _ in blue_sorted[:blue_count]]

        prediction['red_numbers'] = sorted(predicted_red)
        prediction['blue_numbers'] = sorted(predicted_blue)
        prediction['red_probs'] = red_probs
        prediction['blue_probs'] = blue_probs

        return prediction

    def markov_chain_prediction(self, red_freq, blue_freq, trends, repeat_stats):
        """
        基于马尔可夫链进行预测

        Args:
            red_freq: 红球频率统计
            blue_freq: 蓝球频率统计
            trends: 趋势分析结果
            repeat_stats: 重号分析结果

        Returns:
            prediction: 预测结果
        """
        prediction = {}

        if self.lottery_type == "SSQ":
            red_count = 6
            blue_count = 1
            red_range = (1, 33)
            blue_range = (1, 16)
        else:
            red_count = 5
            blue_count = 2
            red_range = (1, 35)
            blue_range = (1, 12)

        # 基于转移概率的预测
        red_probs = {}
        blue_probs = {}

        # 获取最新一期号码
        if self.lottery_type == "SSQ":
            latest_red = set(self.selected_data.iloc[-1, 1:7].tolist())
            latest_blue = set(self.selected_data.iloc[-1, 7:8].tolist())
        else:
            latest_red = set(self.selected_data.iloc[-1, 1:6].tolist())
            latest_blue = set(self.selected_data.iloc[-1, 6:8].tolist())

        # 计算红球预测概率
        for ball in range(red_range[0], red_range[1] + 1):
            # 基础频率权重
            base_weight = red_freq[ball] / sum(red_freq.values()) if sum(red_freq.values()) > 0 else 0

            # 趋势权重
            trend_weight = trends['red_recent'][ball] / sum(trends['red_recent'].values()) if sum(trends['red_recent'].values()) > 0 else 0

            # 重号权重（如果上期出现过，降低概率；如果是常见重号，提高概率）
            repeat_weight = 0
            if ball in latest_red:
                # 上期出现过，根据重号统计调整
                repeat_rate = repeat_stats['red_repeat_numbers'][ball] / len(self.selected_data) if len(self.selected_data) > 0 else 0
                repeat_weight = -0.3 + repeat_rate * 0.5  # 基础降权，但常见重号会有补偿
            else:
                # 上期未出现，正常权重
                repeat_weight = 0.1

            # 综合概率
            red_probs[ball] = base_weight * 0.4 + trend_weight * 0.4 + repeat_weight * 0.2

        # 计算蓝球预测概率
        for ball in range(blue_range[0], blue_range[1] + 1):
            # 基础频率权重
            base_weight = blue_freq[ball] / sum(blue_freq.values()) if sum(blue_freq.values()) > 0 else 0

            # 趋势权重
            trend_weight = trends['blue_recent'][ball] / sum(trends['blue_recent'].values()) if sum(trends['blue_recent'].values()) > 0 else 0

            # 重号权重
            repeat_weight = 0
            if ball in latest_blue:
                repeat_rate = repeat_stats['blue_repeat_numbers'][ball] / len(self.selected_data) if len(self.selected_data) > 0 else 0
                repeat_weight = -0.3 + repeat_rate * 0.5
            else:
                repeat_weight = 0.1

            # 综合概率
            blue_probs[ball] = base_weight * 0.4 + trend_weight * 0.4 + repeat_weight * 0.2

        # 选择概率最高的号码
        red_sorted = sorted(red_probs.items(), key=lambda x: x[1], reverse=True)
        blue_sorted = sorted(blue_probs.items(), key=lambda x: x[1], reverse=True)

        predicted_red = [ball for ball, _ in red_sorted[:red_count]]
        predicted_blue = [ball for ball, _ in blue_sorted[:blue_count]]

        prediction['red_numbers'] = sorted(predicted_red)
        prediction['blue_numbers'] = sorted(predicted_blue)
        prediction['red_probs'] = red_probs
        prediction['blue_probs'] = blue_probs

        return prediction

    def ensemble_prediction(self, bayesian_pred, markov_pred, red_freq, blue_freq, trends):
        """
        集成预测方法，综合多种预测结果

        Args:
            bayesian_pred: 贝叶斯预测结果
            markov_pred: 马尔可夫链预测结果
            red_freq: 红球频率统计
            blue_freq: 蓝球频率统计
            trends: 趋势分析结果

        Returns:
            prediction: 集成预测结果
        """
        prediction = {}

        if self.lottery_type == "SSQ":
            red_count = 6
            blue_count = 1
            red_range = (1, 33)
            blue_range = (1, 16)
        else:
            red_count = 5
            blue_count = 2
            red_range = (1, 35)
            blue_range = (1, 12)

        # 集成红球预测概率
        red_probs = {}
        for ball in range(red_range[0], red_range[1] + 1):
            # 加权平均多种方法的概率
            bayesian_prob = bayesian_pred['red_probs'][ball]
            markov_prob = markov_pred['red_probs'][ball]

            # 集成权重：贝叶斯50%，马尔可夫50%
            red_probs[ball] = bayesian_prob * 0.5 + markov_prob * 0.5

        # 集成蓝球预测概率
        blue_probs = {}
        for ball in range(blue_range[0], blue_range[1] + 1):
            bayesian_prob = bayesian_pred['blue_probs'][ball]
            markov_prob = markov_pred['blue_probs'][ball]

            blue_probs[ball] = bayesian_prob * 0.5 + markov_prob * 0.5

        # 选择概率最高的号码
        red_sorted = sorted(red_probs.items(), key=lambda x: x[1], reverse=True)
        blue_sorted = sorted(blue_probs.items(), key=lambda x: x[1], reverse=True)

        predicted_red = [ball for ball, _ in red_sorted[:red_count]]
        predicted_blue = [ball for ball, _ in blue_sorted[:blue_count]]

        prediction['red_numbers'] = sorted(predicted_red)
        prediction['blue_numbers'] = sorted(predicted_blue)
        prediction['red_probs'] = red_probs
        prediction['blue_probs'] = blue_probs

        return prediction

    def step3_statistical_analysis_and_prediction(self):
        """
        第三步：开始统计分析
        根据用户输入的指令，从相应的数据中开始分析，注意SSQ与DLT的红球蓝球的区别。
        基于全部历史数据，运用数学概率统计相关知识和AI大模型的能力，
        找出潜在的和可能的趋势规律，来预测SSQ或DLT下一期的红球和蓝球号码，并打印相关运算的依据。
        """
        print("\n" + "=" * 60)
        print("第三步：开始统计分析")
        print("=" * 60)

        print(f"正在分析 {self.lottery_type} 彩票数据...")
        print(f"数据总期数: {len(self.selected_data)}")

        # 1. 计算频率统计
        print("\n1. 计算号码出现频率统计...")
        red_freq, blue_freq = self.calculate_frequency_statistics()

        # 打印频率统计结果
        print(f"\n红球出现频率统计（前10个最频繁的号码）:")
        red_sorted = sorted(red_freq.items(), key=lambda x: x[1], reverse=True)[:10]
        for ball, freq in red_sorted:
            percentage = (freq / len(self.selected_data)) * 100
            print(f"  号码 {ball:2d}: 出现 {freq:3d} 次, 频率 {percentage:.2f}%")

        print(f"\n蓝球出现频率统计（前5个最频繁的号码）:")
        blue_sorted = sorted(blue_freq.items(), key=lambda x: x[1], reverse=True)[:5]
        for ball, freq in blue_sorted:
            if self.lottery_type == "SSQ":
                percentage = (freq / len(self.selected_data)) * 100
            else:
                percentage = (freq / (len(self.selected_data) * 2)) * 100  # 大乐透有2个蓝球
            print(f"  号码 {ball:2d}: 出现 {freq:3d} 次, 频率 {percentage:.2f}%")

        # 2. 计算模式分析
        print("\n2. 计算号码模式分析...")
        patterns = self.calculate_pattern_analysis()

        print(f"\n奇偶比例分析（最常见的5种模式）:")
        for pattern, count in patterns['odd_even'].most_common(5):
            percentage = (count / len(self.selected_data)) * 100
            print(f"  奇偶比 {pattern}: 出现 {count:3d} 次, 占比 {percentage:.2f}%")

        print(f"\n大小比例分析（最常见的5种模式）:")
        for pattern, count in patterns['big_small'].most_common(5):
            percentage = (count / len(self.selected_data)) * 100
            print(f"  大小比 {pattern}: 出现 {count:3d} 次, 占比 {percentage:.2f}%")

        print(f"\n质合比例分析（最常见的5种模式）:")
        for pattern, count in patterns['prime_composite'].most_common(5):
            percentage = (count / len(self.selected_data)) * 100
            print(f"  质合比 {pattern}: 出现 {count:3d} 次, 占比 {percentage:.2f}%")

        print(f"\n连号情况分析:")
        for consecutive_count, count in patterns['consecutive'].most_common():
            percentage = (count / len(self.selected_data)) * 100
            print(f"  连号数量 {consecutive_count}: 出现 {count:3d} 次, 占比 {percentage:.2f}%")

        # 3. 计算重号分析
        print("\n3. 计算重号分析（连续两期相同号码）...")
        repeat_stats = self.calculate_repeat_analysis()

        print(f"\n重号统计概况:")
        print(f"  红球重号总次数: {repeat_stats['red_repeat_count']} 次")
        print(f"  蓝球重号总次数: {repeat_stats['blue_repeat_count']} 次")
        print(f"  红球重号期数: {len(repeat_stats['red_repeats'])} 期")
        print(f"  蓝球重号期数: {len(repeat_stats['blue_repeats'])} 期")

        # 显示最常见的重号
        red_repeat_sorted = sorted(repeat_stats['red_repeat_numbers'].items(), key=lambda x: x[1], reverse=True)[:10]
        print(f"\n红球重号频率排行（前10名）:")
        for ball, count in red_repeat_sorted:
            if count > 0:
                print(f"  号码 {ball:2d}: 重号 {count} 次")

        blue_repeat_sorted = sorted(repeat_stats['blue_repeat_numbers'].items(), key=lambda x: x[1], reverse=True)[:5]
        print(f"\n蓝球重号频率排行（前5名）:")
        for ball, count in blue_repeat_sorted:
            if count > 0:
                print(f"  号码 {ball:2d}: 重号 {count} 次")

        # 4. 计算趋势分析
        print("\n4. 计算趋势分析（最近100期）...")
        trends = self.calculate_trend_analysis(100)

        print(f"\n红球热号（最近100期出现最频繁的10个号码）:")
        for ball, freq in trends['red_hot']:
            print(f"  号码 {ball:2d}: 最近100期出现 {freq} 次")

        print(f"\n红球冷号（最近100期出现最少的10个号码）:")
        for ball, freq in trends['red_cold']:
            print(f"  号码 {ball:2d}: 最近100期出现 {freq} 次")

        print(f"\n蓝球热号（最近100期出现最频繁的号码）:")
        for ball, freq in trends['blue_hot']:
            print(f"  号码 {ball:2d}: 最近100期出现 {freq} 次")

        print(f"\n蓝球冷号（最近100期出现最少的号码）:")
        for ball, freq in trends['blue_cold']:
            print(f"  号码 {ball:2d}: 最近100期出现 {freq} 次")

        # 5. 多种预测方法
        print("\n5. 多种预测方法分析...")

        # 5.1 贝叶斯概率预测
        print("\n5.1 基于贝叶斯概率进行预测...")
        bayesian_pred = self.bayesian_prediction(red_freq, blue_freq, trends, patterns)

        # 5.2 马尔可夫链预测
        print("5.2 基于马尔可夫链进行预测...")
        markov_pred = self.markov_chain_prediction(red_freq, blue_freq, trends, repeat_stats)

        # 5.3 集成预测
        print("5.3 基于集成方法进行预测...")
        ensemble_pred = self.ensemble_prediction(bayesian_pred, markov_pred, red_freq, blue_freq, trends)

        # 打印预测结果
        print(f"\n" + "=" * 60)
        print(f"预测结果 - {self.lottery_type}")
        print("=" * 60)

        print(f"\n【贝叶斯预测】")
        print(f"红球号码: {bayesian_pred['red_numbers']}")
        print(f"蓝球号码: {bayesian_pred['blue_numbers']}")

        print(f"\n【马尔可夫链预测】")
        print(f"红球号码: {markov_pred['red_numbers']}")
        print(f"蓝球号码: {markov_pred['blue_numbers']}")

        print(f"\n【集成预测（推荐）】")
        print(f"红球号码: {ensemble_pred['red_numbers']}")
        print(f"蓝球号码: {ensemble_pred['blue_numbers']}")

        # 打印预测依据
        print(f"\n预测依据:")
        print(f"1. 基于全部 {len(self.selected_data)} 期历史数据的频率统计")
        print(f"2. 结合最近100期的趋势分析")
        print(f"3. 考虑重号分析（连续两期相同号码的规律）")
        print(f"4. 运用多种预测方法:")
        print(f"   - 贝叶斯概率：历史频率70% + 近期趋势30%")
        print(f"   - 马尔可夫链：基础频率40% + 趋势40% + 重号权重20%")
        print(f"   - 集成方法：贝叶斯50% + 马尔可夫50%")

        # 打印详细的概率分析（集成预测）
        print(f"\n集成预测概率分析（前10个最有可能的红球号码）:")
        red_prob_sorted = sorted(ensemble_pred['red_probs'].items(), key=lambda x: x[1], reverse=True)[:10]
        for ball, prob in red_prob_sorted:
            print(f"  号码 {ball:2d}: 预测概率 {prob:.4f}")

        print(f"\n集成预测概率分析（前5个最有可能的蓝球号码）:")
        blue_prob_sorted = sorted(ensemble_pred['blue_probs'].items(), key=lambda x: x[1], reverse=True)[:5]
        for ball, prob in blue_prob_sorted:
            print(f"  号码 {ball:2d}: 预测概率 {prob:.4f}")

        # 打印最新一期号码作为参考
        latest_period = self.selected_data.iloc[-1, 0]
        if self.lottery_type == "SSQ":
            latest_red = self.selected_data.iloc[-1, 1:7].tolist()
            latest_blue = self.selected_data.iloc[-1, 7:8].tolist()
        else:
            latest_red = self.selected_data.iloc[-1, 1:6].tolist()
            latest_blue = self.selected_data.iloc[-1, 6:8].tolist()

        print(f"\n参考信息 - 最新一期（第{latest_period}期）:")
        print(f"红球号码: {latest_red}")
        print(f"蓝球号码: {latest_blue}")

        print("\n第三步完成：统计分析和预测完成！")
        return True

    def run_analysis(self):
        """
        运行完整的三步分析流程
        """
        print("开始彩票数据分析三步程序")
        print("=" * 60)

        # 第一步：读取数据
        if not self.step1_read_and_sort_data():
            return False

        # 第二步：指定数据
        if not self.step2_specify_data():
            return False

        # 第三步：统计分析和预测
        if not self.step3_statistical_analysis_and_prediction():
            return False

        print("\n" + "=" * 60)
        print("三步分析流程全部完成！")
        print("=" * 60)

        return True

if __name__ == "__main__":
    # 创建预测器实例
    predictor = LotteryPredictor()

    # 运行完整分析流程
    predictor.run_analysis()
