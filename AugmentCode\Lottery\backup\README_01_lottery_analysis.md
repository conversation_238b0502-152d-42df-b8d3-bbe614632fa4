# 彩票数据分析四步程序（修改版）

## 程序简介

这是一个按照用户要求开发的彩票数据分析程序，严格按照四个步骤进行：

1. **第一步**：读取Excel表格数据并排序
2. **第二步**：指定数据范围、开始行与计算公式
3. **第三步**：开始计算和比对
4. **第四步**：将校核结果保存在Excel文件中

## 最新修改

### 比对校核范围调整
- **原版本**：每次预测只与下一期数据比对
- **修改版**：每次预测与指定行之后的6期数据进行比对（指定行+1至指定行+6）
- **优势**：大大提高了命中检测的机会，更符合实际预测需求

### 循环计算次数调整
- **原版本**：固定进行99次计算
- **修改版**：最多进行500次计算
- **优势**：提供更大的测试样本，获得更可靠的统计结果

## 功能特点

- 支持双色球(SSQ)和大乐透(DLT)两种彩票类型
- 提供三种预测方法：
  - 基于贝叶斯概率预测
  - 基于多条件贝叶斯概率预测
  - 基于全条件贝叶斯概率预测
- **新增**：6期内命中检测机制
- 智能命中率检测（SSQ：红球≥5个+蓝球1个，DLT：红球≥4个+蓝球2个）
- 结果自动保存到Excel文件，包含详细的命中统计

## 文件说明

- `lottery_analysis_four_steps.py` - 主程序文件（修改版）
- `lottery_data_all.xlsx` - 彩票历史数据文件
- `demo.py` - 演示程序（展示6期比对功能）
- `test_quick.py` - 快速测试程序
- `test_modified.py` - 修改版功能测试程序
- `read_lottery_data.py` - 参考的原始程序

## 使用方法

### 1. 运行主程序

```bash
python lottery_analysis_four_steps.py
```

### 2. 按提示进行选择

程序会依次询问：

1. **选择彩票类型**：
   - 输入 `1` 选择双色球(SSQ)
   - 输入 `2` 选择大乐透(DLT)
   - 直接回车使用默认值（双色球）

2. **选择开始行数**：
   - 输入一个数字（建议1000-3000之间）
   - 直接回车使用默认值（2000）

3. **选择计算方法**：
   - 输入 `1` 选择基于贝叶斯概率预测
   - 输入 `2` 选择基于多条件贝叶斯概率预测
   - 输入 `3` 选择基于全条件贝叶斯概率预测
   - 直接回车使用默认值（多条件贝叶斯概率）

### 3. 查看结果

程序运行完成后会生成一个Excel文件，文件名格式为：
`lottery_analysis_results_{彩票类型}_{时间戳}.xlsx`

Excel文件包含以下工作表：
- **详细结果**：每次计算的详细信息，包含6期内命中标记
- **汇总统计**：总体统计信息，包含6期内命中率统计
- **命中详情**：命中的具体情况（如果有命中）

### 4. 新增测试程序

**运行演示程序：**
```bash
python demo.py
```

**运行修改版测试：**
```bash
python test_modified.py
```

## 数据格式要求

程序需要 `lottery_data_all.xlsx` 文件，包含两个工作表：

### SSQ_data_all 工作表
- A列：期号(NO)
- I-O列：彩票号码数据（r1-r6为红球，b为蓝球）

### DLT_data_all 工作表
- A列：期号(NO)
- H-N列：彩票号码数据（r1-r5为红球，b1-b2为蓝球）

## 算法说明

### 贝叶斯概率预测
基于历史数据中各号码的出现概率进行预测。

### 多条件贝叶斯概率预测
基于最新一期号码的跟随性概率，结合历史概率进行预测。

### 全条件贝叶斯概率预测
基于所有历史号码的跟随性概率，结合历史概率进行预测。

## 命中判断标准

### 双色球(SSQ)
- 红球：6个号码中至少命中5个
- 蓝球：1个号码必须命中
- 同时满足上述条件才算命中

### 大乐透(DLT)
- 红球：5个号码中至少命中4个
- 蓝球：2个号码必须全部命中
- 同时满足上述条件才算命中

## 比对机制说明

### 修改前（原版本）
每次预测只与紧接着的下一期数据进行比对，命中率较低。

### 修改后（当前版本）
每次预测与后续6期数据进行比对：
1. 基于指定行的历史数据进行预测
2. 将预测结果与指定行+1至指定行+6的6期数据逐一比对
3. 如果在任何一期中命中，则记录为命中
4. 优先记录最早命中的期数
5. 如果6期内都没有命中，则记录与第一期的比对结果

这种机制大大提高了命中检测的机会，更符合实际彩票预测的应用场景。

## 注意事项

1. 确保 `lottery_data_all.xlsx` 文件存在于程序目录中
2. 开始行数不能超过数据总行数减去6行（需要保证后续有6期数据可比对）
3. 程序会自动清理无效数据和空数据行
4. 所有数据按期号从小到大排序
5. 计算过程可能需要一些时间，请耐心等待
6. **新增**：每次计算最多进行500次，如果数据不足会自动停止

## 快速测试

如果想快速测试程序功能，可以运行：

```bash
python test_quick.py
```

这会进行10次快速计算测试，查看预测效果。

```bash
python test_modified.py
```

这会测试修改后的6期比对功能。

## 技术支持

如有问题，请检查：
1. Python环境是否正确安装
2. 必要的库是否已安装（pandas, numpy, openpyxl）
3. 数据文件是否存在且格式正确
