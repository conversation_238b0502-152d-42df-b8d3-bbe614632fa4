# 彩票数据分析三步程序使用说明

## 程序概述

这是一个基于Python的彩票数据分析程序，按照用户要求的三个步骤进行彩票数据分析和预测。

## 程序功能

### 第一步：读取Excel表格数据并排序
- 读取根目录下的"lottery_data_all.xlsx"文件
- 从"SSQ_data_all"标签页提取A列、I列至O列数据（共8列），保存为ssqhistory_allout变量
  - 第1列数据为期号
  - 第2至第7列数据为每期红球号码
  - 第8列为每期篮球号码
- 从"DLT_data_all"标签页提取A列、H列至N列数据（共8列），保存为dlthistory_allout变量
  - 第1列数据为期号
  - 第2至第6列数据为每期红球号码
  - 第7与第8列为每期篮球号码
- 按第一列（NO列）序号从小到大排列
- 自动清空无效数据或空数据行

### 第二步：指定数据
- 程序询问用户想选择1.SSQ还是2.DLT
- 如果用户选择1，则基于ssqhistory_allout进行后续计算与分析
- 如果用户选择2，则基于dlthistory_allout进行后续计算与分析
- 如果用户不选择，等待30s后，默认选择1.SSQ

### 第三步：开始统计分析
基于全部历史数据，运用数学概率统计相关知识和AI大模型的能力，找出潜在的和可能的趋势规律，来预测SSQ或DLT下一期的红球和蓝球号码。

分析内容包括：

1. **号码出现频率统计**
   - 统计每个号码在历史数据中的出现次数和频率
   - 显示红球和蓝球的频率排行榜

2. **号码模式分析**
   - 奇偶比例分析：统计红球中奇数和偶数的比例分布
   - 大小比例分析：统计红球中大号和小号的比例分布
   - **质合比例分析**：统计红球中质数和合数的比例分布
   - 连号情况分析：统计连续号码出现的情况

3. **重号分析**
   - 分析连续两期出现相同号码的情况
   - 统计各号码的重号频率
   - 识别最常见的重号模式

4. **趋势分析**
   - **扩展至最近100期**的号码出现趋势分析
   - 识别热号（出现频繁的号码）和冷号（出现较少的号码）
   - 基于更长期的数据提供更准确的趋势判断

5. **多种预测方法**
   - **贝叶斯概率预测**：综合历史频率（权重70%）和近期趋势（权重30%）
   - **马尔可夫链预测**：基础频率40% + 趋势40% + 重号权重20%
   - **集成预测（推荐）**：综合贝叶斯和马尔可夫链方法，各占50%权重
   - 输出多种预测结果供参考

## 使用方法

### 运行程序
```bash
python lottery_analysis_three_steps.py
```

### 交互过程
1. 程序自动执行第一步，读取Excel数据
2. 程序询问选择彩票类型：
   - 输入 `1` 选择双色球(SSQ)
   - 输入 `2` 选择大乐透(DLT)
   - 直接回车或等待30秒将默认选择双色球
3. 程序自动执行统计分析和预测

### 输出结果
程序会详细输出：
- 数据读取和处理信息
- 频率统计结果
- 模式分析结果（包括奇偶、大小、质合比例）
- 重号分析结果
- 趋势分析结果（最近100期）
- 多种预测结果：
  - 贝叶斯预测
  - 马尔可夫链预测
  - 集成预测（推荐）
- 详细的预测依据和概率分析
- 最新一期号码作为参考

## 文件要求

程序需要在同一目录下有 `lottery_data_all.xlsx` 文件，包含：
- `SSQ_data_all` 工作表：双色球历史数据
- `DLT_data_all` 工作表：大乐透历史数据

## 预测原理

### 双色球(SSQ)
- 红球：从1-33中选择6个号码
- 蓝球：从1-16中选择1个号码

### 大乐透(DLT)
- 红球：从1-35中选择5个号码
- 蓝球：从1-12中选择2个号码

### 算法特点
1. **数据驱动**：基于全部历史数据进行分析
2. **多维度分析**：结合频率、模式、重号、趋势等多个维度
3. **多种预测方法**：
   - 贝叶斯概率理论
   - 马尔可夫链状态转移
   - 集成学习方法
4. **扩展分析**：
   - 趋势分析扩展至100期
   - 新增质合比例分析
   - 重号模式识别
5. **透明度高**：详细输出分析依据和计算过程

## 注意事项

1. 彩票具有随机性，本程序仅供学习和研究使用
2. 预测结果不保证准确性，请理性对待
3. 程序基于历史数据分析，过往表现不代表未来结果
4. 请确保Excel文件格式正确且数据完整

## 技术特点

- 使用Python pandas库进行数据处理
- 实现了完整的数据清洗和排序功能
- 支持用户交互和超时处理
- **新增功能**：
  - 质数判断算法
  - 重号分析算法
  - 马尔可夫链状态转移预测
  - 集成学习预测方法
- 提供详细的分析报告和预测依据
- 代码结构清晰，注释详细

## 更新日志

### 最新版本更新内容：
1. **趋势分析扩展**：从20期扩展到100期，提供更长期的趋势判断
2. **重号分析**：新增连续两期相同号码的分析功能
3. **质合比例分析**：新增质数与合数比例的模式分析
4. **多种预测方法**：
   - 保留原有的贝叶斯概率预测
   - 新增马尔可夫链预测方法
   - 新增集成预测方法（推荐使用）
5. **更全面的分析报告**：提供更详细的统计信息和预测依据
