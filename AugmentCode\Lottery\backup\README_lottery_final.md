# 彩票数据分析程序使用说明

## 程序概述

`lottery_final.py` 是一个基于贝叶斯概率理论的彩票数据分析程序，支持双色球(SSQ)和大乐透(DLT)两种彩票类型的预测和分析。

## 最新改进 (v2.4)

1. **优化程序流程**: 先选择彩票类型，再读取相应数据，提高效率
2. **智能数据读取**: 根据用户选择只读取对应彩票类型的数据，节省内存和加载时间
3. **简化用户交互**: 取消30秒超时默认选择，直接等待用户输入
4. **增强错误处理**: 用户输入错误时提示重新输入，而不是直接退出程序
5. **优化显示格式**: 实际号码显示时红球和蓝球用"+"分隔，更直观易懂
6. **修正文字描述**:
   - 多注预测显示"基于多注预测方法"而非"多条件贝叶斯"
   - 比对结果标题改为"与后续6期号码比对结果"
7. **改进显示格式**: 所有概率值以百分比形式显示，更直观易懂
8. **完善连续分析功能**:
   - 修复号码显示格式：去除np.int64()，直接显示为整数
   - 优化Excel输出：所有号码用"+"分隔红球和蓝球
   - 改进最佳匹配逻辑：比较三种预测方法，选择命中数最多的
   - 新增预测方法列：标明最佳匹配来自哪种预测方法
   - 优先级处理：相同命中数时按优先级选择预测方法
9. **修复多注预测号码显示**:
   - 修正号码数量：SSQ红球7个+蓝球2个，DLT红球6个+蓝球3个
   - 独特显示格式：多注预测使用"红球[...] + 蓝球[...]"格式
   - 区别化显示：与两注预测方法的显示格式有明显区别
   - 完整信息展示：在所有功能中保持一致的正确显示

## 主要功能

### 1. 彩票类型选择
- **选项1**: SSQ (双色球) - 红球1-33选6个，蓝球1-16选1个
- **选项2**: DLT (大乐透) - 红球1-35选5个，蓝球1-12选2个

### 2. 操作模式

#### 预测模式
- **两注号码预测**: 使用多条件贝叶斯和全条件贝叶斯方法各生成一注号码
- **多注号码预测**: 使用多条件贝叶斯方法生成多个候选号码
  - SSQ: 红球前7个，蓝球前2个
  - DLT: 红球前6个，蓝球前3个

#### 分析模式
- **单次分析**: 分析指定期号的预测准确性
- **连续分析**: 从指定期号开始连续分析最多500期，结果保存到Excel

## 使用方法

### 运行程序
```bash
python lottery_final.py
```

### 操作流程

1. **选择彩票类型**
   - 输入1选择双色球
   - 输入2选择大乐透

2. **选择操作模式**
   - 输入1选择预测模式
   - 输入2选择分析模式

3. **根据选择执行相应操作**

#### 数据读取优化示例

**选择双色球时**:
```
正在读取SSQ数据并排序...
正在读取双色球数据...
处理 双色球(SSQ) 数据...

双色球(SSQ) 数据信息:
行数: 3312, 列数: 8
期号范围: 3001 - 25066
```

**选择大乐透时**:
```
正在读取DLT数据并排序...
正在读取大乐透数据...
处理 大乐透(DLT) 数据...

大乐透(DLT) 数据信息:
行数: 2733, 列数: 8
期号范围: 7001 - 25065
```

#### 预测模式示例输出

**两注号码预测**:
```
上一期SSQ第25066期的号码是：
红球：[6, 22, 24, 27, 28, 30]
蓝球：[4]

基于'多条件贝叶斯概率预测'方法预测的号码是：
红球：[7, 17, 20, 22, 26, 27]
蓝球：[16]

基于'全条件贝叶斯概率预测'方法预测的号码是：
红球：[1, 6, 14, 17, 22, 26]
蓝球：[1]
```

**多注号码预测**:
```
基于'多注预测'方法预测的号码是：
红球[26, 27, 22, 20, 17, 7, 14] + 蓝球[14, 16]

红球概率排序（从大到小）：
  1. 号码 26: 概率 0.6309%
  2. 号码 27: 概率 0.5973%
  ...

蓝球概率排序（从大到小）：
  1. 号码 14: 概率 0.4855%
  2. 号码 16: 概率 0.4855%
```

#### 多注预测修复示例

**修复前的问题**:
- SSQ红球只显示6个，应该显示7个
- SSQ蓝球只显示1个，应该显示2个
- DLT红球应该显示6个，蓝球应该显示3个
- 显示格式与两注预测相同，缺乏区分

**修复后的效果**:
```
双色球多注预测：
红球[26, 27, 22, 20, 17, 7, 14] + 蓝球[14, 16]  # 7个红球，2个蓝球

大乐透多注预测：
红球[29, 35, 22, 30, 34, 32] + 蓝球[10, 7, 5]  # 6个红球，3个蓝球
```

#### 错误处理改进示例

**输入错误处理**:
```
请输入要分析的期号（例如：23001）: abc
输入错误，请重新输入期号（请输入数字）
请输入要分析的期号（例如：23001）: 999999
输入错误，请重新输入期号（未找到期号 999999）
请输入要分析的期号（例如：23001）: 25060
正在分析第 25060 期...
```

#### 显示格式改进示例

**号码显示格式**:
```
与后续6期号码比对结果：
第 25060 期：实际号码 [6, 14, 18, 25, 28, 30] + [1]，多条件贝叶斯命中 2 个
第 25061 期：实际号码 [6, 7, 9, 10, 11, 32] + [9]，多条件贝叶斯命中 1 个

最佳匹配结果：
期号：25060
实际号码：[6, 14, 18, 25, 28, 30] + [1]
命中数量：2 个
```

#### 连续分析改进示例

**Excel输出格式**:
```
连续分析序号  分析期号  两注多条件贝叶斯预测              两注全条件贝叶斯预测              多注预测号码                    最佳匹配期号  最佳匹配号码                    最佳匹配命中数  最佳匹配预测方法
1           25055    [6, 7, 14, 17, 18, 26] + [5]   [1, 14, 17, 22, 26, 32] + [1]   [26, 14, 7, 17, 6, 18] + [13]   25060       [6, 14, 18, 25, 28, 30] + [1]   3             两注多条件贝叶斯预测
2           25056    [7, 14, 17, 22, 26, 27] + [16] [1, 14, 17, 22, 26, 32] + [16] [14, 26, 17, 22, 7, 27] + [6]   25056       [1, 2, 10, 14, 28, 31] + [3]    2             两注全条件贝叶斯预测
```

**改进要点**:
- 号码格式：红球和蓝球用"+"分隔，清晰直观
- 最佳匹配：比较三种预测方法，选择命中数最多的
- 方法标识：新增列标明最佳匹配来自哪种预测方法
- 优先级：相同命中数时按优先级选择（多条件>全条件>多注）

#### 分析模式

**单次分析**: 输入期号(如25060)，程序会分析该期的预测准确性
**连续分析**: 输入起始期号，程序会连续分析多期并保存结果到Excel

## 核心算法

### 多条件贝叶斯概率预测
基于历史数据的球号出现频率和跟随性统计，计算每个号码的预测概率。

### 全条件贝叶斯概率预测
考虑所有历史条件的全面概率计算方法。

## 数据要求

程序需要 `lottery_data_all.xlsx` 文件，包含：
- `SSQ_data_all` 工作表：双色球历史数据
- `DLT_data_all` 工作表：大乐透历史数据

数据格式：A列为期号，后续列为开奖号码。

## 输出文件

连续分析模式会生成Excel文件：
- 文件名格式：`continuous_analysis_results_{彩票类型}_{时间戳}.xlsx`
- 包含详细分析结果和汇总统计

## 注意事项

1. 程序基于历史数据进行概率计算，不保证预测准确性
2. 彩票具有随机性，请理性购买
3. 建议定期更新历史数据以提高预测效果
4. 程序运行需要pandas、numpy等依赖库

## 技术特点

- 面向对象设计，代码结构清晰
- 支持用户交互，操作简便
- 多种预测算法，结果可对比
- 完整的分析流程，结果可追溯
- Excel输出，便于后续分析
