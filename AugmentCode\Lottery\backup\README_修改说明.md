# 01_lottery_analysis.py 程序修改说明

## 修改概述

根据用户要求，对 `01_lottery_analysis.py` 程序进行了以下修改：

1. **补充用户选择选项**：添加了选项4、5、6，分别对应贝叶斯预测、马尔可夫预测和集成预测
2. **修改默认选项**：将默认选择从2改为6（基于集成预测）
3. **集成AI预测方法**：从 `02_read_calc_lottery_AI.py` 程序中移植了相关预测算法
4. **扩展比对范围**：将比对校核范围从6期扩展到12期

## 详细修改内容

### 1. 用户选择界面修改

**修改前：**
```
请选择计算方法:
1. 基于贝叶斯概率预测
2. 基于多条件贝叶斯概率预测
3. 基于全条件贝叶斯概率预测
请输入选择 (1、2 或 3，30秒后默认选择2)
```

**修改后：**
```
请选择计算方法:
1. 基于贝叶斯概率预测
2. 基于多条件贝叶斯概率预测
3. 基于全条件贝叶斯概率预测
4. 基于贝叶斯预测
5. 基于马尔可夫预测
6. 基于集成预测
请输入选择 (1、2、3、4、5 或 6，30秒后默认选择6)
```

### 2. 新增预测方法

#### 选项4：基于贝叶斯预测 (`bayesian_ai`)
- **来源**：`02_read_calc_lottery_AI.py` 的 `bayesian_prediction` 方法
- **算法特点**：
  - 历史频率权重：70%
  - 近期趋势权重：30%
  - 分析最近100期数据的趋势

#### 选项5：基于马尔可夫预测 (`markov`)
- **来源**：`02_read_calc_lottery_AI.py` 的 `markov_chain_prediction` 方法
- **算法特点**：
  - 基础频率权重：40%
  - 趋势权重：40%
  - 重号权重：20%
  - 考虑连续两期的重号分析
  - 对上期出现的号码进行权重调整

#### 选项6：基于集成预测 (`ensemble`) - **默认选项**
- **来源**：`02_read_calc_lottery_AI.py` 的 `ensemble_prediction` 方法
- **算法特点**：
  - 贝叶斯预测权重：50%
  - 马尔可夫预测权重：50%
  - 综合多种方法的优势

### 3. 新增辅助方法

#### `is_prime(n)`
- **功能**：判断一个数是否为质数
- **用途**：支持质合比例分析

#### `calculate_repeat_analysis()`
- **功能**：计算重号分析（连续两期的相同号码）
- **返回**：重号统计结果，包括：
  - 红球重号记录和次数
  - 蓝球重号记录和次数
  - 各号码的重号频率

#### `bayesian_prediction_ai(train_data)`
- **功能**：AI版贝叶斯预测
- **特点**：结合历史频率和近期趋势

#### `markov_chain_prediction(train_data)`
- **功能**：马尔可夫链预测
- **特点**：基于转移概率和重号分析

#### `ensemble_prediction(train_data)`
- **功能**：集成预测
- **特点**：综合多种预测方法

### 4. 比对范围扩展

将比对校核的范围从6期扩展到12期：

**修改前：**
- 比对范围：指定行+1 至 指定行+6（共6期）
- 数据要求：需要确保后面还有6期数据可以比对
- 建议开始行：1000 - (总行数-100)

**修改后：**
- 比对范围：指定行+1 至 指定行+12（共12期）
- 数据要求：需要确保后面还有12期数据可以比对
- 建议开始行：1000 - (总行数-112)

**具体修改：**
- 循环检查：`for j in range(1, 7)` → `for j in range(1, 13)`
- 结果字段：`hit_in_6_periods` → `hit_in_12_periods`
- Excel输出：`6期内命中` → `12期内命中`
- 统计汇总：`6期内命中次数/率` → `12期内命中次数/率`

### 5. 核心逻辑修改

在 `predict_numbers` 方法中添加了对新预测方法的支持：

```python
# 新增的AI预测方法
if method == "bayesian_ai":
    return self.bayesian_prediction_ai(train_data)
elif method == "markov":
    return self.markov_chain_prediction(train_data)
elif method == "ensemble":
    return self.ensemble_prediction(train_data)
```

## 保持不变的功能

1. **四步分析流程**：完整保持原有的四步流程结构
2. **原有预测方法**：选项1、2、3的贝叶斯方法保持不变
3. **Excel保存格式**：结果保存格式保持不变（字段名有更新）
4. **命中率计算**：SSQ和DLT的命中判断逻辑保持不变
5. **用户交互**：超时机制和输入处理保持不变
6. **计算次数**：仍然进行最多500次计算和比对

## 使用说明

1. **运行程序**：`python 01_lottery_analysis.py`
2. **选择彩票类型**：SSQ或DLT（默认SSQ）
3. **设置开始行数**：建议1000-数据总行数-100（默认2000）
4. **选择计算方法**：1-6任选（默认6：集成预测）
5. **等待计算完成**：程序将自动进行500次计算和比对
6. **查看结果**：结果将保存到Excel文件中

## 技术特点

1. **算法先进性**：集成了多种现代预测算法
2. **代码复用性**：充分利用了02程序的成熟算法
3. **向后兼容性**：完全保持原有功能不变
4. **用户友好性**：提供更多选择，默认推荐最优方法
5. **结果可靠性**：通过集成方法提高预测准确性
6. **验证全面性**：12期比对范围提供更全面的预测效果评估

## 文件结构

- `01_lottery_analysis.py`：修改后的主程序
- `02_read_calc_lottery_AI.py`：参考的AI预测程序
- `lottery_data_all.xlsx`：数据文件
- `README_修改说明.md`：本说明文档

## 注意事项

1. 确保 `lottery_data_all.xlsx` 文件存在且格式正确
2. 新增的预测方法需要足够的历史数据支持
3. 集成预测方法计算时间可能稍长，但准确性更高
4. 建议使用默认的集成预测方法以获得最佳效果
5. **重要**：由于比对范围扩展到12期，需要确保数据文件包含足够的历史期数
6. 选择开始行时注意新的建议范围（总行数-112）
7. 关注"12期内命中率"这个重要的新指标
