# 彩票数据分析四步程序

## 程序概述

这是一个按照用户要求开发的四步彩票数据分析程序，能够读取Excel彩票数据，进行预测分析，并将结果保存到Excel文件中。

## 功能特点

### 第一步：读取Excel表格数据并排序
- 读取根目录下的"lottery_data_all.xlsx"文件
- 从"SSQ_data_all"标签页提取A列、I列至O列数据（共8列），保存为ssqhistory_allout变量
  - 第1列数据为期号，第2至第7列数据为每期红球号码，第8列为每期篮球号码
- 从"DLT_data_all"标签页提取A列、H列至N列数据（共8列），保存为dlthistory_allout变量
  - 第1列数据为期号，第2至第6列数据为每期红球号码，第7与第8列为每期篮球号码
- 按第一列（NO列）序号从小到大排列
- 自动清空无效数据或空数据行

### 第二步：指定数据范围、开始行与计算公式
1. **彩票类型选择**：
   - 程序询问用户想选择1.SSQ还是2.DLT
   - 如果用户选择1，则基于ssqhistory_allout进行后续计算与分析
   - 如果用户选择2，则基于dlthistory_allout进行后续计算与分析
   - 如果用户不选择，等待30s后，默认选择1.SSQ

2. **开始行选择**：
   - 程序询问用户选择从哪一行开始
   - 用户输入的数字不能超过数据范围
   - 如果用户不选择，等待30s后，默认选择第2000行

3. **计算方法选择**：
   - 1：贝叶斯预测
   - 2：马尔可夫预测  
   - 3：集成预测
   - 如果用户不选择，等待30s后，默认选择3（集成预测）

### 第三步：开始计算
- 根据用户输入的指令，从相应的数据中，从指定行开始
- 以指定行之前的数据（期数小于等于指定行的期数）作为数据库
- 按用户指定的方法预测SSQ或DLT下一期的号码
- 与指定行之后的数据（期数大于指定行的期数）进行比对
- 比对校核的范围规定为指定行之后的6期数据以内，即指定行+1行至指定行+6行
- **命中条件**：
  - SSQ：7个号码中，红球有至少5个号码相等，篮球1个号码相等
  - DLT：7个号码中，红球有至少4个号码相等，篮球2个号码相等
- 计算次数最多500次
- 根据比对结果按要求反馈

### 第四步：将校核结果保存在Excel文件中
- **Excel格式**：完全按照01_lottery_analysis.py的格式保存
- **文件命名**：lottery_analysis_results_{彩票类型}_{时间戳}.xlsx
- **工作表结构**：
  - **详细结果**：包含所有计算结果的详细信息
    - 列名：计算次序, 基于期号, 比对期号, 计算方法, 预测号码, 实际号码, 红球命中数, 蓝球命中数, 总命中数, 是否命中, 6期内命中
  - **汇总统计**：包含统计汇总信息
    - 统计项目：彩票类型, 计算方法, 开始行数, 总计算次数, 总命中次数, 命中率(%), 6期内命中次数, 6期内命中率(%), 平均红球匹配数, 平均蓝球匹配数, 平均总匹配数
  - **命中详情**：只包含命中结果的详细信息
    - 列名：计算次序, 基于期号, 比对期号, 预测号码, 实际号码, 命中详情, 6期内命中

## 预测算法

### 1. 贝叶斯预测
- 基于历史频率统计进行预测
- 计算每个号码的出现概率
- 选择概率最高的号码组合

### 2. 马尔可夫预测
- 考虑号码的转移概率
- 基于最近一期的号码调整预测权重
- 降低重号出现的概率

### 3. 集成预测（推荐）
- 综合贝叶斯和马尔可夫两种方法
- 取两种方法的交集和并集
- 如果交集不够，从高频号码中补充

## 文件结构

```
AugmentCode/Lottery/
├── lottery_analysis_four_steps.py    # 主程序文件
├── test_four_steps.py                # 测试程序
├── test_print_data.py                # 打印功能测试程序
├── test_excel_format.py              # Excel格式测试程序
├── test_excel_format_demo.py         # Excel格式演示程序
├── 演示打印功能.py                    # 打印功能演示程序
├── 运行示例.py                       # 使用示例
├── lottery_data_all.xlsx             # 彩票数据文件（需要用户提供）
└── README_四步彩票分析程序.md         # 说明文档
```

## 使用方法

### 1. 准备数据文件
确保在程序目录下有"lottery_data_all.xlsx"文件，包含：
- "SSQ_data_all"工作表：双色球数据
- "DLT_data_all"工作表：大乐透数据

### 2. 运行程序
```bash
python lottery_analysis_four_steps.py
```

### 3. 按提示操作
- 选择彩票类型（SSQ或DLT）
- 选择开始行号
- 选择计算方法
- 等待程序完成计算和保存结果

### 4. 查看结果
程序会自动生成Excel结果文件，包含：
- **详细结果**：所有计算结果的详细信息
- **汇总统计**：统计汇总信息（命中率、平均匹配数等）
- **命中详情**：只包含命中结果的详细信息

### 5. 查看完整数据
程序在完成四步流程后，会自动打印ssqhistory_allout中的所有数据，包括：
- 所有期号的完整双色球数据
- 数据统计信息
- 红球和蓝球的频率分析

## 测试程序

运行测试程序验证功能：
```bash
python test_four_steps.py
```

测试程序会：
1. 创建模拟数据
2. 测试各个步骤的功能
3. 验证预测算法
4. 可选择运行完整程序

### Excel格式测试

测试Excel保存格式：
```bash
python test_excel_format.py
```

演示Excel保存格式：
```bash
python test_excel_format_demo.py
```

### 打印功能测试

测试打印功能：
```bash
python test_print_data.py
```

演示打印功能：
```bash
python 演示打印功能.py
```

## 注意事项

1. **数据格式要求**：
   - Excel文件必须包含指定的工作表名称
   - 数据列必须按照指定格式排列
   - 期号必须是数字格式

2. **性能考虑**：
   - 大数据量时计算可能需要较长时间
   - 建议从较小的开始行开始测试

3. **命中率说明**：
   - 彩票具有随机性，命中率可能较低
   - 程序主要用于数据分析和算法验证
   - 不保证实际投注的盈利性

## 技术特点

- **模块化设计**：四个步骤分别实现，便于维护和扩展
- **用户友好**：支持超时默认值，避免程序卡死
- **错误处理**：完善的异常处理机制
- **结果保存**：自动生成详细的Excel报告
- **算法多样**：提供三种不同的预测算法

## 依赖库

```python
pandas>=1.3.0
numpy>=1.20.0
openpyxl>=3.0.0
```

安装依赖：
```bash
pip install pandas numpy openpyxl
```

## 版本信息

- 版本：1.0
- 开发日期：2024年
- 开发语言：Python 3.7+
- 兼容性：Windows/Linux/macOS
