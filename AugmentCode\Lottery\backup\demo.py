#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
彩票分析程序演示
自动运行一个完整的分析流程
"""

from lottery_analysis_four_steps import LotteryAnalyzer

def demo_analysis():
    """演示完整的分析流程"""
    print("=" * 60)
    print("彩票分析程序演示")
    print("=" * 60)
    
    # 创建分析器
    analyzer = LotteryAnalyzer()
    
    # 第一步：读取数据
    print("\n正在执行第一步：读取Excel数据...")
    if not analyzer.step1_read_and_sort_data():
        print("数据读取失败！")
        return
    
    # 第二步：设置参数（自动设置，不需要用户输入）
    print("\n正在执行第二步：设置分析参数...")
    analyzer.lottery_type = "SSQ"  # 选择双色球
    analyzer.data = analyzer.ssqhistory_allout
    analyzer.start_row = 2000  # 从第2000行开始
    analyzer.method = "multi_bayesian"  # 使用多条件贝叶斯方法
    
    print(f"设置完成：")
    print(f"  彩票类型: {analyzer.lottery_type}")
    print(f"  开始行数: {analyzer.start_row}")
    print(f"  计算方法: {analyzer.method}")
    
    # 第三步：计算和比对（只做10次演示）
    print("\n正在执行第三步：计算和比对（演示10次）...")
    print("每次预测将与后续6期数据进行比对...")
    analyzer.results = []
    hit_count = 0

    for i in range(10):
        current_row = analyzer.start_row + i

        # 检查是否有足够的后续数据
        if current_row + 6 >= len(analyzer.data):
            print(f"第{i+1}次计算：后续数据不足6期，跳过")
            continue

        # 获取训练数据
        train_data = analyzer.data.iloc[:current_row].copy()

        # 预测号码
        predicted_numbers = analyzer.predict_numbers(train_data, analyzer.method)

        if predicted_numbers is None:
            continue

        # 与后续6期数据进行比对
        hit_found = False
        best_hit_info = None
        best_target_period = None
        best_actual_numbers = None

        print(f"  第{i+1}次计算:")
        print(f"    基于期号: {analyzer.data.iloc[current_row - 1, 0]}")
        print(f"    预测号码: {predicted_numbers}")
        print(f"    比对后续6期数据:")

        for j in range(1, 7):  # 检查后续6期数据
            target_row = current_row + j
            actual_numbers = analyzer.data.iloc[target_row, 1:].values.tolist()

            # 检查命中情况
            hit_info = analyzer.check_hit_rate(predicted_numbers, actual_numbers)

            print(f"      期号{analyzer.data.iloc[target_row, 0]}: {actual_numbers} -> 红球{hit_info['red_hits']}个, 蓝球{hit_info['blue_hits']}个", end="")

            if hit_info['is_hit']:
                hit_found = True
                best_hit_info = hit_info
                best_target_period = analyzer.data.iloc[target_row, 0]
                best_actual_numbers = actual_numbers
                print(" *** 命中！***")
                break
            else:
                print()

        # 如果没有命中，记录与第一期的比对结果
        if not hit_found:
            target_row = current_row + 1
            actual_numbers = analyzer.data.iloc[target_row, 1:].values.tolist()
            hit_info = analyzer.check_hit_rate(predicted_numbers, actual_numbers)
            best_target_period = analyzer.data.iloc[target_row, 0]
            best_actual_numbers = actual_numbers
            best_hit_info = hit_info

        # 记录结果
        result = {
            'iteration': i + 1,
            'base_period': analyzer.data.iloc[current_row - 1, 0],
            'target_period': best_target_period,
            'method': analyzer.method,
            'predicted_numbers': predicted_numbers,
            'actual_numbers': best_actual_numbers,
            'red_hits': best_hit_info['red_hits'],
            'blue_hits': best_hit_info['blue_hits'],
            'total_hits': best_hit_info['total_hits'],
            'is_hit': best_hit_info['is_hit'],
            'hit_in_6_periods': hit_found
        }

        analyzer.results.append(result)

        if best_hit_info['is_hit']:
            hit_count += 1

        print()

    print(f"演示完成！10次计算中命中{hit_count}次")
    
    # 第四步：保存结果
    print("\n正在执行第四步：保存结果到Excel...")
    if analyzer.step4_save_results_to_excel():
        print("结果保存成功！")
    else:
        print("结果保存失败！")
    
    print("\n" + "=" * 60)
    print("演示完成！")
    print("=" * 60)

if __name__ == "__main__":
    demo_analysis()
