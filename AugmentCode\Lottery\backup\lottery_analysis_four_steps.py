#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
彩票数据分析四步程序
按照用户要求的四个步骤进行彩票数据分析：

第一步：读取Excel表格数据并排序
- 读取根目录下的"lottery_data_all.xlsx"文件
- 从"SSQ_data_all"标签页提取A列、I列至O列数据（共8列），保存为ssqhistory_allout变量
- 从"DLT_data_all"标签页提取A列、H列至N列数据（共8列），保存为dlthistory_allout变量
- 按第一列（NO列）序号从小到大排列
- 自动清空无效数据或空数据行

第二步：指定数据范围、开始行与计算公式
- 程序询问用户想选择1.SSQ还是2.DLT
- 程序询问用户选择从哪一行开始
- 程序询问用户想按哪一种方法计算（贝叶斯、马尔可夫、集成预测）

第三步：开始计算
- 根据用户输入的指令，从相应的数据中，从指定行开始预测
- 与指定行之后的数据进行比对，比对校核的范围规定为指定行之后的6期数据以内
- 计算次数最多500次

第四步：将校核结果保存在Excel文件中
"""

import pandas as pd
import numpy as np
from collections import Counter
import signal
import sys
import time
from datetime import datetime

class LotteryAnalysisFourSteps:
    """彩票分析四步程序类"""
    
    def __init__(self, file_path="lottery_data_all.xlsx"):
        """
        初始化彩票分析器
        
        Args:
            file_path: Excel文件路径
        """
        self.file_path = file_path
        self.ssqhistory_allout = None
        self.dlthistory_allout = None
        self.selected_data = None
        self.lottery_type = None
        self.start_row = None
        self.calculation_method = None
        self.results = []  # 存储校核结果
        
    def step1_read_and_sort_data(self):
        """
        第一步：读取Excel表格数据并排序
        """
        print("=" * 60)
        print("第一步：读取Excel表格数据并排序")
        print("=" * 60)
        
        try:
            # 读取双色球数据
            print("正在读取双色球数据...")
            ssq_data = pd.read_excel(self.file_path, sheet_name="SSQ_data_all")
            
            # 提取A列、I列至O列数据（共8列）
            # A列是第0列，I列至O列是第8-14列
            # 第1列数据为期号，第2至第7列数据为每期红球号码，第8列为每期篮球号码
            self.ssqhistory_allout = ssq_data.iloc[:, [0] + list(range(8, 15))].copy()
            
            # 读取大乐透数据
            print("正在读取大乐透数据...")
            dlt_data = pd.read_excel(self.file_path, sheet_name="DLT_data_all")
            
            # 提取A列、H列至N列数据（共8列）
            # A列是第0列，H列至N列是第7-13列
            # 第1列数据为期号，第2至第6列数据为每期红球号码，第7与第8列为每期篮球号码
            self.dlthistory_allout = dlt_data.iloc[:, [0] + list(range(7, 14))].copy()
            
            # 清理和排序数据
            datasets = {
                "ssqhistory_allout": self.ssqhistory_allout,
                "dlthistory_allout": self.dlthistory_allout
            }
            
            for name, dataset in datasets.items():
                print(f"处理 {name} 数据...")
                
                # 删除包含NaN的行（自动清空无效数据或空数据行）
                dataset.dropna(inplace=True)
                
                # 按第一列（NO列）从小到大排序
                dataset.sort_values(by=dataset.columns[0], inplace=True)
                
                # 重置索引
                dataset.reset_index(drop=True, inplace=True)
                
                # 确保数据类型一致性
                try:
                    # 第一列是期号，应该是整数类型
                    dataset.iloc[:, 0] = dataset.iloc[:, 0].astype(int)
                    
                    # 其他列是彩票号码，也应该是整数类型
                    for col in range(1, dataset.shape[1]):
                        dataset.iloc[:, col] = dataset.iloc[:, col].astype(int)
                except ValueError as e:
                    print(f"警告: 转换 {name} 的数据类型时出错: {e}")
                    # 使用更安全的方法
                    for col in range(dataset.shape[1]):
                        dataset.iloc[:, col] = dataset.iloc[:, col].fillna(0).astype(int)
            
            # 打印数据信息
            print(f"\nssqhistory_allout 数据信息:")
            print(f"行数: {self.ssqhistory_allout.shape[0]}, 列数: {self.ssqhistory_allout.shape[1]}")
            print(f"期号范围: {self.ssqhistory_allout.iloc[0, 0]} - {self.ssqhistory_allout.iloc[-1, 0]}")
            print(f"列名: {list(self.ssqhistory_allout.columns)}")
            
            print(f"\ndlthistory_allout 数据信息:")
            print(f"行数: {self.dlthistory_allout.shape[0]}, 列数: {self.dlthistory_allout.shape[1]}")
            print(f"期号范围: {self.dlthistory_allout.iloc[0, 0]} - {self.dlthistory_allout.iloc[-1, 0]}")
            print(f"列名: {list(self.dlthistory_allout.columns)}")
            
            print("\n第一步完成：数据读取和排序成功！")
            return True
            
        except FileNotFoundError:
            print(f"错误: 找不到文件 '{self.file_path}'，请确保文件存在。")
            return False
        except Exception as e:
            print(f"错误: {str(e)}")
            return False

    def get_user_input_with_timeout(self, prompt, timeout=30, default_value=None):
        """
        获取用户输入，支持超时
        
        Args:
            prompt: 提示信息
            timeout: 超时时间（秒）
            default_value: 默认值
            
        Returns:
            用户输入或默认值
        """
        print(f"{prompt}（{timeout}秒后将使用默认值: {default_value}）")
        
        # 使用简单的方式：直接获取输入，不实现复杂的超时机制
        try:
            user_input = input("请输入（直接回车使用默认值）: ")
            if user_input.strip() == "":
                print(f"使用默认值: {default_value}")
                return str(default_value) if default_value is not None else ""
            return user_input.strip()
        except KeyboardInterrupt:
            print(f"\n用户中断，使用默认值: {default_value}")
            return str(default_value) if default_value is not None else ""

    def step2_specify_parameters(self):
        """
        第二步：指定数据范围、开始行与计算公式
        """
        print("\n" + "=" * 60)
        print("第二步：指定数据范围、开始行与计算公式")
        print("=" * 60)
        
        # 1. 选择彩票类型
        print("\n请选择彩票类型:")
        print("1. SSQ (双色球)")
        print("2. DLT (大乐透)")
        
        lottery_choice = self.get_user_input_with_timeout(
            "请输入选择 (1 或 2，30秒后默认选择1): ", 30, "1"
        )
        
        if lottery_choice == "2":
            self.lottery_type = "DLT"
            self.selected_data = self.dlthistory_allout
            print("您选择了：大乐透 (DLT)")
            print("将基于dlthistory_allout进行后续计算与分析")
        else:
            self.lottery_type = "SSQ"
            self.selected_data = self.ssqhistory_allout
            print("您选择了：双色球 (SSQ)")
            print("将基于ssqhistory_allout进行后续计算与分析")
        
        # 2. 选择开始行
        max_row = len(self.selected_data)
        print(f"\n数据总行数: {max_row}")
        print("请选择从哪一行开始计算:")
        
        start_row_input = self.get_user_input_with_timeout(
            f"请输入开始行号 (1-{max_row}，30秒后默认选择2000): ", 30, "2000"
        )
        
        try:
            self.start_row = int(start_row_input)
            if self.start_row < 1 or self.start_row > max_row:
                print(f"输入的行号超出范围，使用默认值2000")
                self.start_row = min(2000, max_row)
        except ValueError:
            print(f"输入无效，使用默认值2000")
            self.start_row = min(2000, max_row)
        
        print(f"选择的开始行: {self.start_row}")
        
        # 3. 选择计算方法
        print("\n请选择计算方法:")
        print("1. 贝叶斯预测")
        print("2. 马尔可夫预测")
        print("3. 集成预测")
        
        method_choice = self.get_user_input_with_timeout(
            "请输入选择 (1、2或3，30秒后默认选择3): ", 30, "3"
        )
        
        if method_choice == "1":
            self.calculation_method = "贝叶斯预测"
        elif method_choice == "2":
            self.calculation_method = "马尔可夫预测"
        else:
            self.calculation_method = "集成预测"
        
        print(f"选择的计算方法: {self.calculation_method}")
        
        print("\n第二步完成：参数指定成功！")
        return True

    def calculate_frequency_statistics(self, data_subset):
        """
        计算号码出现频率统计

        Args:
            data_subset: 用于计算的数据子集

        Returns:
            red_freq: 红球频率统计
            blue_freq: 蓝球频率统计
        """
        if self.lottery_type == "SSQ":
            # 双色球：红球1-33，蓝球1-16
            red_columns = range(1, 7)  # 第2-7列是红球
            blue_columns = [7]  # 第8列是蓝球
            red_range = (1, 33)
            blue_range = (1, 16)
        else:  # DLT
            # 大乐透：红球1-35，蓝球1-12
            red_columns = range(1, 6)  # 第2-6列是红球
            blue_columns = [6, 7]  # 第7-8列是蓝球
            red_range = (1, 35)
            blue_range = (1, 12)

        # 统计红球频率
        red_freq = {i: 0 for i in range(red_range[0], red_range[1] + 1)}
        for col in red_columns:
            for value in data_subset.iloc[:, col]:
                if red_range[0] <= value <= red_range[1]:
                    red_freq[value] += 1

        # 统计蓝球频率
        blue_freq = {i: 0 for i in range(blue_range[0], blue_range[1] + 1)}
        for col in blue_columns:
            for value in data_subset.iloc[:, col]:
                if blue_range[0] <= value <= blue_range[1]:
                    blue_freq[value] += 1

        return red_freq, blue_freq

    def bayesian_prediction(self, data_subset):
        """
        基于贝叶斯概率进行预测

        Args:
            data_subset: 用于预测的历史数据

        Returns:
            prediction: 预测结果
        """
        red_freq, blue_freq = self.calculate_frequency_statistics(data_subset)

        if self.lottery_type == "SSQ":
            red_count = 6
            blue_count = 1
            red_range = (1, 33)
            blue_range = (1, 16)
        else:
            red_count = 5
            blue_count = 2
            red_range = (1, 35)
            blue_range = (1, 12)

        # 计算红球预测概率
        red_probs = {}
        total_red_freq = sum(red_freq.values())

        for ball in range(red_range[0], red_range[1] + 1):
            # 基础概率（历史频率）
            base_prob = red_freq[ball] / total_red_freq if total_red_freq > 0 else 1 / (red_range[1] - red_range[0] + 1)
            red_probs[ball] = base_prob

        # 计算蓝球预测概率
        blue_probs = {}
        total_blue_freq = sum(blue_freq.values())

        for ball in range(blue_range[0], blue_range[1] + 1):
            # 基础概率（历史频率）
            base_prob = blue_freq[ball] / total_blue_freq if total_blue_freq > 0 else 1 / (blue_range[1] - blue_range[0] + 1)
            blue_probs[ball] = base_prob

        # 选择概率最高的号码
        red_sorted = sorted(red_probs.items(), key=lambda x: x[1], reverse=True)
        blue_sorted = sorted(blue_probs.items(), key=lambda x: x[1], reverse=True)

        predicted_red = [ball for ball, _ in red_sorted[:red_count]]
        predicted_blue = [ball for ball, _ in blue_sorted[:blue_count]]

        return {
            'red_numbers': sorted(predicted_red),
            'blue_numbers': sorted(predicted_blue)
        }

    def markov_prediction(self, data_subset):
        """
        基于马尔可夫链进行预测

        Args:
            data_subset: 用于预测的历史数据

        Returns:
            prediction: 预测结果
        """
        red_freq, blue_freq = self.calculate_frequency_statistics(data_subset)

        if self.lottery_type == "SSQ":
            red_count = 6
            blue_count = 1
            red_range = (1, 33)
            blue_range = (1, 16)
            red_columns = range(1, 7)
            blue_columns = [7]
        else:
            red_count = 5
            blue_count = 2
            red_range = (1, 35)
            blue_range = (1, 12)
            red_columns = range(1, 6)
            blue_columns = [6, 7]

        # 获取最新一期号码
        if len(data_subset) > 0:
            latest_red = set(data_subset.iloc[-1, red_columns].tolist())
            latest_blue = set(data_subset.iloc[-1, blue_columns].tolist())
        else:
            latest_red = set()
            latest_blue = set()

        # 计算红球预测概率（考虑转移概率）
        red_probs = {}
        for ball in range(red_range[0], red_range[1] + 1):
            # 基础频率权重
            base_weight = red_freq[ball] / sum(red_freq.values()) if sum(red_freq.values()) > 0 else 1 / (red_range[1] - red_range[0] + 1)

            # 重号权重（如果上期出现过，降低概率）
            repeat_weight = 0.3 if ball in latest_red else 1.0

            red_probs[ball] = base_weight * repeat_weight

        # 计算蓝球预测概率
        blue_probs = {}
        for ball in range(blue_range[0], blue_range[1] + 1):
            # 基础频率权重
            base_weight = blue_freq[ball] / sum(blue_freq.values()) if sum(blue_freq.values()) > 0 else 1 / (blue_range[1] - blue_range[0] + 1)

            # 重号权重
            repeat_weight = 0.3 if ball in latest_blue else 1.0

            blue_probs[ball] = base_weight * repeat_weight

        # 选择概率最高的号码
        red_sorted = sorted(red_probs.items(), key=lambda x: x[1], reverse=True)
        blue_sorted = sorted(blue_probs.items(), key=lambda x: x[1], reverse=True)

        predicted_red = [ball for ball, _ in red_sorted[:red_count]]
        predicted_blue = [ball for ball, _ in blue_sorted[:blue_count]]

        return {
            'red_numbers': sorted(predicted_red),
            'blue_numbers': sorted(predicted_blue)
        }

    def ensemble_prediction(self, data_subset):
        """
        集成预测方法，综合多种预测结果

        Args:
            data_subset: 用于预测的历史数据

        Returns:
            prediction: 集成预测结果
        """
        # 获取贝叶斯和马尔可夫预测结果
        bayesian_pred = self.bayesian_prediction(data_subset)
        markov_pred = self.markov_prediction(data_subset)

        # 简单的集成方法：取两种方法的交集，如果交集不够，则补充高频号码
        red_freq, blue_freq = self.calculate_frequency_statistics(data_subset)

        if self.lottery_type == "SSQ":
            red_count = 6
            blue_count = 1
        else:
            red_count = 5
            blue_count = 2

        # 红球集成
        red_intersection = list(set(bayesian_pred['red_numbers']) & set(markov_pred['red_numbers']))
        red_union = list(set(bayesian_pred['red_numbers']) | set(markov_pred['red_numbers']))

        # 如果交集不够，从并集中补充
        if len(red_intersection) < red_count:
            remaining_needed = red_count - len(red_intersection)
            remaining_candidates = [x for x in red_union if x not in red_intersection]
            red_intersection.extend(remaining_candidates[:remaining_needed])

        # 如果还不够，从高频号码中补充
        if len(red_intersection) < red_count:
            red_sorted = sorted(red_freq.items(), key=lambda x: x[1], reverse=True)
            for ball, _ in red_sorted:
                if ball not in red_intersection:
                    red_intersection.append(ball)
                    if len(red_intersection) >= red_count:
                        break

        # 蓝球集成
        blue_intersection = list(set(bayesian_pred['blue_numbers']) & set(markov_pred['blue_numbers']))
        blue_union = list(set(bayesian_pred['blue_numbers']) | set(markov_pred['blue_numbers']))

        # 如果交集不够，从并集中补充
        if len(blue_intersection) < blue_count:
            remaining_needed = blue_count - len(blue_intersection)
            remaining_candidates = [x for x in blue_union if x not in blue_intersection]
            blue_intersection.extend(remaining_candidates[:remaining_needed])

        # 如果还不够，从高频号码中补充
        if len(blue_intersection) < blue_count:
            blue_sorted = sorted(blue_freq.items(), key=lambda x: x[1], reverse=True)
            for ball, _ in blue_sorted:
                if ball not in blue_intersection:
                    blue_intersection.append(ball)
                    if len(blue_intersection) >= blue_count:
                        break

        return {
            'red_numbers': sorted(red_intersection[:red_count]),
            'blue_numbers': sorted(blue_intersection[:blue_count])
        }

    def predict_numbers(self, data_subset):
        """
        根据选择的方法预测号码

        Args:
            data_subset: 用于预测的历史数据

        Returns:
            prediction: 预测结果
        """
        if self.calculation_method == "贝叶斯预测":
            return self.bayesian_prediction(data_subset)
        elif self.calculation_method == "马尔可夫预测":
            return self.markov_prediction(data_subset)
        else:  # 集成预测
            return self.ensemble_prediction(data_subset)

    def check_prediction_accuracy(self, predicted, actual):
        """
        检查预测准确性

        Args:
            predicted: 预测号码
            actual: 实际号码

        Returns:
            match_info: 匹配信息
        """
        if self.lottery_type == "SSQ":
            # 双色球：红球至少5个相等，蓝球1个相等
            red_columns = range(1, 7)
            blue_columns = [7]
            min_red_match = 5
            min_blue_match = 1
        else:
            # 大乐透：红球至少4个相等，蓝球2个相等
            red_columns = range(1, 6)
            blue_columns = [6, 7]
            min_red_match = 4
            min_blue_match = 2

        # 获取实际号码
        actual_red = set(actual.iloc[red_columns].tolist())
        actual_blue = set(actual.iloc[blue_columns].tolist())

        # 计算匹配数量
        predicted_red_set = set(predicted['red_numbers'])
        predicted_blue_set = set(predicted['blue_numbers'])

        red_matches = len(predicted_red_set & actual_red)
        blue_matches = len(predicted_blue_set & actual_blue)

        # 判断是否满足条件
        is_hit = (red_matches >= min_red_match and blue_matches >= min_blue_match)

        return {
            'is_hit': is_hit,
            'red_matches': red_matches,
            'blue_matches': blue_matches,
            'total_matches': red_matches + blue_matches,
            'predicted_red': predicted['red_numbers'],
            'predicted_blue': predicted['blue_numbers'],
            'actual_red': sorted(list(actual_red)),
            'actual_blue': sorted(list(actual_blue))
        }

    def step3_calculation_and_verification(self):
        """
        第三步：开始计算
        根据用户输入的指令，从相应的数据中，从指定行开始，以指定行之前的数据作为数据库，
        按用户指定的方法预测号码，并与指定行之后的数据进行比对，计算次数最多500次。
        """
        print("\n" + "=" * 60)
        print("第三步：开始计算")
        print("=" * 60)

        print(f"彩票类型: {self.lottery_type}")
        print(f"开始行: {self.start_row}")
        print(f"计算方法: {self.calculation_method}")
        print(f"数据总行数: {len(self.selected_data)}")

        # 确保有足够的数据进行预测和验证
        max_iterations = min(500, len(self.selected_data) - self.start_row - 6)
        if max_iterations <= 0:
            print("错误：数据不足，无法进行预测和验证。")
            return False

        print(f"将进行 {max_iterations} 次预测和验证")

        self.results = []
        hit_count = 0

        for i in range(max_iterations):
            current_row = self.start_row + i

            # 获取用于预测的历史数据（从开始到当前行）
            training_data = self.selected_data.iloc[:current_row].copy()

            # 预测下一期号码
            prediction = self.predict_numbers(training_data)

            # 获取接下来6期的实际数据进行验证
            verification_start = current_row
            verification_end = min(current_row + 6, len(self.selected_data))

            # 检查每一期的匹配情况
            for j in range(verification_start, verification_end):
                if j >= len(self.selected_data):
                    break

                actual_data = self.selected_data.iloc[j]
                match_info = self.check_prediction_accuracy(prediction, actual_data)

                if match_info['is_hit']:
                    hit_count += 1
                    result = {
                        'prediction_period': int(training_data.iloc[-1, 0]),  # 基于第几期预测
                        'actual_period': int(actual_data.iloc[0]),  # 第几期实际号码
                        'method': self.calculation_method,
                        'predicted_red': match_info['predicted_red'],
                        'predicted_blue': match_info['predicted_blue'],
                        'actual_red': match_info['actual_red'],
                        'actual_blue': match_info['actual_blue'],
                        'red_matches': match_info['red_matches'],
                        'blue_matches': match_info['blue_matches'],
                        'total_matches': match_info['total_matches']
                    }
                    self.results.append(result)

                    print(f"命中！基于第{result['prediction_period']}期号码用{result['method']}预测的结果与第{result['actual_period']}期号码中了{result['total_matches']}个号码")
                    print(f"  预测红球: {result['predicted_red']}, 实际红球: {result['actual_red']}, 匹配: {result['red_matches']}")
                    print(f"  预测蓝球: {result['predicted_blue']}, 实际蓝球: {result['actual_blue']}, 匹配: {result['blue_matches']}")

            # 显示进度
            if (i + 1) % 50 == 0:
                print(f"已完成 {i + 1}/{max_iterations} 次预测，当前命中次数: {hit_count}")

        print(f"\n第三步完成：共进行了 {max_iterations} 次预测，命中 {hit_count} 次，命中率: {hit_count/max_iterations*100:.2f}%")
        return True

    def step4_save_results(self):
        """
        第四步：将校核结果保存在Excel文件中
        参考01_lottery_analysis.py中的格式
        """
        print("\n" + "=" * 60)
        print("第四步：将校核结果保存在Excel文件中")
        print("=" * 60)

        if not self.results:
            print("没有结果数据可保存")
            return False

        try:
            # 创建结果DataFrame，按照参考格式
            results_data = []
            for result in self.results:
                # 格式化预测号码和实际号码
                if self.lottery_type == "SSQ":
                    pred_red = result['predicted_red']
                    pred_blue = result['predicted_blue']
                    actual_red = result['actual_red']
                    actual_blue = result['actual_blue']
                    predicted_numbers = pred_red + pred_blue
                    actual_numbers = actual_red + actual_blue
                else:  # DLT
                    pred_red = result['predicted_red']
                    pred_blue = result['predicted_blue']
                    actual_red = result['actual_red']
                    actual_blue = result['actual_blue']
                    predicted_numbers = pred_red + pred_blue
                    actual_numbers = actual_red + actual_blue

                row = {
                    '计算次序': len(results_data) + 1,
                    '基于期号': result['prediction_period'],
                    '比对期号': result['actual_period'],
                    '计算方法': result['method'],
                    '预测号码': str(predicted_numbers),
                    '实际号码': str(actual_numbers),
                    '红球命中数': result['red_matches'],
                    '蓝球命中数': result['blue_matches'],
                    '总命中数': result['total_matches'],
                    '是否命中': '是' if result['is_hit'] else '否',
                    '6期内命中': '是'  # 我们的程序是在6期内检查的
                }
                results_data.append(row)

            results_df = pd.DataFrame(results_data)

            # 生成文件名，按照参考格式
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"lottery_analysis_results_{self.lottery_type}_{timestamp}.xlsx"

            # 保存到Excel文件
            with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                # 保存详细结果
                results_df.to_excel(writer, sheet_name='详细结果', index=False)

                # 创建汇总统计，按照参考格式
                total_hits = len(self.results)  # 我们只保存命中的结果
                total_hits_in_6 = len(self.results)  # 所有结果都是6期内的

                summary_data = {
                    '统计项目': [
                        '彩票类型',
                        '计算方法',
                        '开始行数',
                        '总计算次数',
                        '总命中次数',
                        '命中率(%)',
                        '6期内命中次数',
                        '6期内命中率(%)',
                        '平均红球匹配数',
                        '平均蓝球匹配数',
                        '平均总匹配数'
                    ],
                    '数值': [
                        self.lottery_type,
                        self.calculation_method,
                        self.start_row,
                        500,  # 最多计算500次
                        total_hits,
                        f"{total_hits/500*100:.2f}",  # 基于500次计算的命中率
                        total_hits_in_6,
                        f"{total_hits_in_6/500*100:.2f}",
                        f"{sum(r['red_matches'] for r in self.results)/len(self.results):.2f}" if self.results else "0.00",
                        f"{sum(r['blue_matches'] for r in self.results)/len(self.results):.2f}" if self.results else "0.00",
                        f"{sum(r['total_matches'] for r in self.results)/len(self.results):.2f}" if self.results else "0.00"
                    ]
                }
                summary_df = pd.DataFrame(summary_data)
                summary_df.to_excel(writer, sheet_name='汇总统计', index=False)

                # 保存命中详情，按照参考格式
                if self.results:
                    hit_data = []
                    for i, result in enumerate(self.results):
                        if self.lottery_type == "SSQ":
                            pred_red = result['predicted_red']
                            pred_blue = result['predicted_blue']
                            actual_red = result['actual_red']
                            actual_blue = result['actual_blue']
                            predicted_numbers = pred_red + pred_blue
                            actual_numbers = actual_red + actual_blue
                        else:  # DLT
                            pred_red = result['predicted_red']
                            pred_blue = result['predicted_blue']
                            actual_red = result['actual_red']
                            actual_blue = result['actual_blue']
                            predicted_numbers = pred_red + pred_blue
                            actual_numbers = actual_red + actual_blue

                        row = {
                            '计算次序': i + 1,
                            '基于期号': result['prediction_period'],
                            '比对期号': result['actual_period'],
                            '预测号码': str(predicted_numbers),
                            '实际号码': str(actual_numbers),
                            '命中详情': f"红球{result['red_matches']}个，蓝球{result['blue_matches']}个",
                            '6期内命中': '是'
                        }
                        hit_data.append(row)

                    hit_df = pd.DataFrame(hit_data)
                    hit_df.to_excel(writer, sheet_name='命中详情', index=False)

            print(f"结果已保存到文件: {filename}")
            print(f"包含工作表: 详细结果、汇总统计、命中详情")
            print(f"共保存了 {len(self.results)} 条命中记录")

            # 显示简要统计
            if len(self.results) > 0:
                avg_red = sum(r['red_matches'] for r in self.results) / len(self.results)
                avg_blue = sum(r['blue_matches'] for r in self.results) / len(self.results)
                avg_total = sum(r['total_matches'] for r in self.results) / len(self.results)

                print(f"\n简要统计:")
                print(f"平均红球匹配数: {avg_red:.2f}")
                print(f"平均蓝球匹配数: {avg_blue:.2f}")
                print(f"平均总匹配数: {avg_total:.2f}")

            print("\n第四步完成：结果保存成功！")
            return True

        except Exception as e:
            print(f"保存结果时出错: {str(e)}")
            return False

    def run_four_steps_analysis(self):
        """
        运行完整的四步分析流程
        """
        print("开始彩票数据分析四步程序")
        print("=" * 60)

        # 第一步：读取数据
        if not self.step1_read_and_sort_data():
            print("第一步失败，程序终止。")
            return False

        # 第二步：指定参数
        if not self.step2_specify_parameters():
            print("第二步失败，程序终止。")
            return False

        # 第三步：计算和验证
        if not self.step3_calculation_and_verification():
            print("第三步失败，程序终止。")
            return False

        # 第四步：保存结果
        if not self.step4_save_results():
            print("第四步失败，程序终止。")
            return False

        print("\n" + "=" * 60)
        print("四步分析流程全部完成！")
        print("=" * 60)

        return True

if __name__ == "__main__":
    # 创建分析器实例
    analyzer = LotteryAnalysisFourSteps()

    # 运行完整四步分析流程
    try:
        analyzer.run_four_steps_analysis()
    except KeyboardInterrupt:
        print("\n\n程序被用户中断。")
    except Exception as e:
        print(f"\n\n程序运行出错: {str(e)}")
        import traceback
        traceback.print_exc()
