#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试连续分析功能的改进
"""

from lottery_final import LotteryAnalyzer
import unittest.mock
import pandas as pd

def test_continuous_analysis_improvements():
    """测试连续分析功能的改进"""
    print("=" * 80)
    print("测试连续分析功能改进")
    print("=" * 80)
    
    analyzer = LotteryAnalyzer()
    analyzer.lottery_type = "SSQ"
    analyzer.read_and_sort_data()
    analyzer.data = analyzer.ssqhistory_allout
    
    # 模拟用户输入起始期号25050，只分析3期来测试
    print("模拟连续分析（只分析3期用于测试）...")
    
    # 手动执行连续分析的核心逻辑，但只分析3期
    start_period = 25050
    start_row = None
    for i, row in analyzer.data.iterrows():
        if row.iloc[0] == start_period:
            start_row = i
            break
    
    if start_row is None:
        print(f"未找到期号 {start_period}")
        return False
    
    analysis_results = []
    
    # 只分析3期
    for i in range(3):
        current_row = start_row + i
        current_period = analyzer.data.iloc[current_row, 0]
        
        # 检查是否超出数据范围
        if current_row + 6 >= len(analyzer.data):
            print(f"第 {current_period} 期后续数据不足6期，停止分析")
            break
        
        # 使用当前期号之前的数据作为训练数据
        train_data = analyzer.data.iloc[:current_row].copy()
        
        if len(train_data) < 10:
            print(f"第 {current_period} 期训练数据不足，跳过")
            continue
        
        # 获取当前期号及后续5期的实际号码（共6期作为标准）
        standard_periods = []
        for j in range(6):
            period_row = current_row + j
            period_number = analyzer.data.iloc[period_row, 0]
            actual_numbers = analyzer.data.iloc[period_row, 1:8].tolist()
            standard_periods.append({
                'period': period_number,
                'numbers': actual_numbers
            })
        
        # 进行预测
        multi_prediction = analyzer.predict_numbers(train_data, "multi_bayesian")
        full_prediction = analyzer.predict_numbers(train_data, "full_bayesian")
        
        # 进行多注号码预测
        red_probs, blue_probs = analyzer.calculate_multi_bayesian_probs_for_data(train_data)
        
        red_sorted = sorted(red_probs.items(), key=lambda x: x[1], reverse=True)
        blue_sorted = sorted(blue_probs.items(), key=lambda x: x[1], reverse=True)
        multi_pred_red = [int(ball) for ball, _ in red_sorted[:7]]  # SSQ: 7个红球
        multi_pred_blue = [int(ball) for ball, _ in blue_sorted[:2]]  # SSQ: 2个蓝球

        # 用于命中率计算的完整号码列表（按标准格式）
        multi_pred_numbers = multi_pred_red[:6] + multi_pred_blue[:1]  # 命中率计算用标准格式
        
        # 与标准6期进行比对，找出三种预测方法中的最佳匹配
        best_match = None
        max_hits = -1
        best_method = ""
        
        for period_info in standard_periods:
            period_num = period_info['period']
            actual_numbers = period_info['numbers']
            
            # 计算三种预测方法的命中数
            multi_hit_info = analyzer.check_hit_rate(multi_prediction, actual_numbers)
            multi_hits = multi_hit_info['total_hits']
            
            full_hit_info = analyzer.check_hit_rate(full_prediction, actual_numbers)
            full_hits = full_hit_info['total_hits']
            
            multi_pred_hit_info = analyzer.check_hit_rate(multi_pred_numbers, actual_numbers)
            multi_pred_hits = multi_pred_hit_info['total_hits']
            
            # 找出当前期号中命中数最多的预测方法
            current_max_hits = max(multi_hits, full_hits, multi_pred_hits)
            current_best_method = ""
            
            if current_max_hits == multi_hits:
                current_best_method = "两注多条件贝叶斯预测"
            elif current_max_hits == full_hits:
                current_best_method = "两注全条件贝叶斯预测"
            else:
                current_best_method = "多注预测"
            
            # 如果有多个方法命中数相同，按优先级选择
            if current_max_hits == multi_hits and current_max_hits == full_hits and current_max_hits == multi_pred_hits:
                current_best_method = "两注多条件贝叶斯预测"
            elif current_max_hits == multi_hits and current_max_hits == full_hits:
                current_best_method = "两注多条件贝叶斯预测"
            elif current_max_hits == multi_hits and current_max_hits == multi_pred_hits:
                current_best_method = "两注多条件贝叶斯预测"
            elif current_max_hits == full_hits and current_max_hits == multi_pred_hits:
                current_best_method = "两注全条件贝叶斯预测"
            
            # 更新全局最佳匹配
            if current_max_hits > max_hits or (current_max_hits == max_hits and (best_match is None or period_num < best_match['period'])):
                max_hits = current_max_hits
                best_match = {
                    'period': period_num,
                    'numbers': actual_numbers,
                    'hits': current_max_hits
                }
                best_method = current_best_method
        
        # 格式化预测号码显示
        multi_prediction_formatted = analyzer.format_lottery_numbers([int(x) for x in multi_prediction])
        full_prediction_formatted = analyzer.format_lottery_numbers([int(x) for x in full_prediction])
        # 多注预测号码使用特殊格式，显示完整的红球和蓝球数量
        multi_pred_numbers_formatted = analyzer.format_multi_prediction_numbers(multi_pred_red, multi_pred_blue)
        best_match_numbers_formatted = analyzer.format_lottery_numbers(best_match['numbers'])
        
        # 记录分析结果
        result = {
            'sequence': i + 1,
            'analysis_period': current_period,
            'multi_bayesian_prediction': multi_prediction_formatted,
            'full_bayesian_prediction': full_prediction_formatted,
            'multi_numbers_prediction': multi_pred_numbers_formatted,
            'best_match_period': best_match['period'],
            'best_match_numbers': best_match_numbers_formatted,
            'best_match_hits': best_match['hits'],
            'best_match_method': best_method
        }
        
        analysis_results.append(result)
        
        print(f"第 {i+1} 期分析完成：期号 {current_period}")
        print(f"  两注多条件贝叶斯预测：{multi_prediction_formatted}")
        print(f"  两注全条件贝叶斯预测：{full_prediction_formatted}")
        print(f"  多注预测号码：{multi_pred_numbers_formatted}")
        print(f"  最佳匹配：期号 {best_match['period']}，号码 {best_match_numbers_formatted}，命中 {best_match['hits']} 个")
        print(f"  最佳匹配方法：{best_method}")
        print()
    
    # 创建DataFrame并显示
    if analysis_results:
        results_data = []
        for result in analysis_results:
            row = {
                '连续分析序号': result['sequence'],
                '分析期号': result['analysis_period'],
                '两注多条件贝叶斯预测': result['multi_bayesian_prediction'],
                '两注全条件贝叶斯预测': result['full_bayesian_prediction'],
                '多注预测号码': result['multi_numbers_prediction'],
                '最佳匹配期号': result['best_match_period'],
                '最佳匹配号码': result['best_match_numbers'],
                '最佳匹配命中数': result['best_match_hits'],
                '最佳匹配预测方法': result['best_match_method']
            }
            results_data.append(row)
        
        results_df = pd.DataFrame(results_data)
        print("=" * 80)
        print("连续分析结果DataFrame预览：")
        print("=" * 80)
        print(results_df.to_string(index=False))
        
        # 保存到Excel文件进行验证
        filename = "test_continuous_analysis_results.xlsx"
        with pd.ExcelWriter(filename, engine='openpyxl') as writer:
            results_df.to_excel(writer, sheet_name='连续分析结果', index=False)
        
        print(f"\n测试结果已保存到文件: {filename}")
        return True
    
    return False

if __name__ == "__main__":
    print("开始测试连续分析功能改进...")
    test_continuous_analysis_improvements()
    print("\n测试完成！")
