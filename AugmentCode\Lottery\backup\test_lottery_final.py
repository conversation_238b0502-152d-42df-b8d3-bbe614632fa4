#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试lottery_final.py的各种功能
"""

from lottery_final import LotteryAnalyzer

def test_two_numbers_prediction():
    """测试两注号码预测"""
    print("=" * 60)
    print("测试两注号码预测功能")
    print("=" * 60)

    analyzer = LotteryAnalyzer()

    # 设置为SSQ
    analyzer.lottery_type = "SSQ"

    # 读取数据（现在只会读取SSQ数据）
    if not analyzer.read_and_sort_data():
        print("数据读取失败")
        return False

    analyzer.data = analyzer.ssqhistory_allout

    # 执行两注号码预测
    result = analyzer.two_numbers_prediction()
    print(f"两注号码预测结果: {result}")
    return result

def test_multi_numbers_prediction():
    """测试多注号码预测"""
    print("\n" + "=" * 60)
    print("测试多注号码预测功能")
    print("=" * 60)

    analyzer = LotteryAnalyzer()

    # 设置为SSQ
    analyzer.lottery_type = "SSQ"

    # 读取数据（现在只会读取SSQ数据）
    if not analyzer.read_and_sort_data():
        print("数据读取失败")
        return False

    analyzer.data = analyzer.ssqhistory_allout

    # 执行多注号码预测
    result = analyzer.multi_numbers_prediction()
    print(f"多注号码预测结果: {result}")
    return result

def test_single_analysis():
    """测试单次分析"""
    print("\n" + "=" * 60)
    print("测试单次分析功能")
    print("=" * 60)

    analyzer = LotteryAnalyzer()

    # 设置为SSQ
    analyzer.lottery_type = "SSQ"

    # 读取数据（现在只会读取SSQ数据）
    if not analyzer.read_and_sort_data():
        print("数据读取失败")
        return False

    analyzer.data = analyzer.ssqhistory_allout

    # 模拟用户输入期号25060
    import unittest.mock
    with unittest.mock.patch.object(analyzer, 'get_user_input', return_value="25060"):
        result = analyzer.single_analysis()

    print(f"单次分析结果: {result}")
    return result

def test_input_error_handling():
    """测试输入错误处理"""
    print("\n" + "=" * 60)
    print("测试输入错误处理功能")
    print("=" * 60)

    analyzer = LotteryAnalyzer()

    # 设置为SSQ
    analyzer.lottery_type = "SSQ"

    # 读取数据
    if not analyzer.read_and_sort_data():
        print("数据读取失败")
        return False

    analyzer.data = analyzer.ssqhistory_allout

    # 模拟用户先输入错误，再输入正确的期号
    import unittest.mock
    inputs = ["abc", "999999", "25060"]  # 先输入非数字，再输入不存在的期号，最后输入正确期号
    with unittest.mock.patch.object(analyzer, 'get_user_input', side_effect=inputs):
        result = analyzer.single_analysis()

    print(f"输入错误处理测试结果: {result}")
    return result

def test_dlt_prediction():
    """测试大乐透预测功能"""
    print("\n" + "=" * 60)
    print("测试大乐透预测功能")
    print("=" * 60)

    analyzer = LotteryAnalyzer()

    # 设置为DLT
    analyzer.lottery_type = "DLT"

    # 读取数据（现在只会读取DLT数据）
    if not analyzer.read_and_sort_data():
        print("数据读取失败")
        return False

    analyzer.data = analyzer.dlthistory_allout

    # 执行两注号码预测
    result = analyzer.two_numbers_prediction()
    print(f"大乐透预测结果: {result}")
    return result

if __name__ == "__main__":
    print("开始测试lottery_final.py的各种功能...")

    # 测试两注号码预测
    test_two_numbers_prediction()

    # 测试多注号码预测
    test_multi_numbers_prediction()

    # 测试单次分析
    test_single_analysis()

    # 测试输入错误处理
    test_input_error_handling()

    # 测试大乐透预测
    test_dlt_prediction()

    print("\n" + "=" * 60)
    print("所有测试完成！")
    print("=" * 60)
