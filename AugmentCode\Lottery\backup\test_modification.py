#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试修改后的比对逻辑
验证新的比对方式是否正确工作
"""

import pandas as pd
import numpy as np

def test_hit_comparison_logic():
    """
    测试新的比对逻辑
    """
    print("测试新的比对逻辑...")
    
    # 模拟预测号码
    predicted_numbers = [1, 5, 10, 15, 20, 25, 8]  # SSQ格式：6个红球 + 1个蓝球
    
    # 模拟12期实际号码数据
    actual_data = [
        [2, 6, 11, 16, 21, 26, 9],   # 期号1：命中0个
        [1, 6, 11, 16, 21, 26, 9],   # 期号2：命中1个红球
        [1, 5, 11, 16, 21, 26, 9],   # 期号3：命中2个红球
        [1, 5, 10, 16, 21, 26, 9],   # 期号4：命中3个红球
        [1, 5, 10, 15, 21, 26, 9],   # 期号5：命中4个红球
        [1, 5, 10, 15, 20, 26, 9],   # 期号6：命中5个红球
        [1, 5, 10, 15, 20, 25, 9],   # 期号7：命中6个红球
        [1, 5, 10, 15, 20, 25, 8],   # 期号8：命中6个红球+1个蓝球（完全命中）
        [1, 5, 10, 15, 20, 26, 8],   # 期号9：命中5个红球+1个蓝球
        [1, 5, 10, 15, 21, 26, 8],   # 期号10：命中4个红球+1个蓝球
        [1, 5, 10, 16, 21, 26, 8],   # 期号11：命中3个红球+1个蓝球
        [1, 5, 11, 16, 21, 26, 8],   # 期号12：命中2个红球+1个蓝球
    ]
    
    def check_hit_rate_test(predicted, actual):
        """
        测试版本的命中检查函数
        """
        # SSQ：前6个是红球，第7个是蓝球
        pred_red = set(predicted[:6])
        pred_blue = set(predicted[6:7])
        actual_red = set(actual[:6])
        actual_blue = set(actual[6:7])

        red_hits = len(pred_red & actual_red)
        blue_hits = len(pred_blue & actual_blue)

        # SSQ中奖条件：红球至少5个相等，蓝球1个相等
        is_hit = red_hits >= 5 and blue_hits == 1

        return {
            'red_hits': red_hits,
            'blue_hits': blue_hits,
            'total_hits': red_hits + blue_hits,
            'is_hit': is_hit
        }
    
    # 测试新的比对逻辑
    best_hit_info = None
    best_period = None
    best_actual_numbers = None
    max_hit_count = -1
    closest_period_index = None
    
    print(f"预测号码: {predicted_numbers}")
    print("\n逐期比对结果:")
    
    for j in range(1, 13):  # 检查12期数据
        actual_numbers = actual_data[j-1]
        hit_info = check_hit_rate_test(predicted_numbers, actual_numbers)
        current_hit_count = hit_info['total_hits']
        
        print(f"期号{j}: 实际号码{actual_numbers}, 命中{current_hit_count}个 (红球{hit_info['red_hits']}, 蓝球{hit_info['blue_hits']}), 中奖: {'是' if hit_info['is_hit'] else '否'}")
        
        # 如果当前期号的命中数量更多，或者命中数量相同但期号更近，则更新最佳结果
        if (current_hit_count > max_hit_count or 
            (current_hit_count == max_hit_count and (closest_period_index is None or j < closest_period_index))):
            max_hit_count = current_hit_count
            best_hit_info = hit_info
            best_period = j
            best_actual_numbers = actual_numbers
            closest_period_index = j
    
    print(f"\n最终选择结果:")
    print(f"最佳匹配期号: {best_period}")
    print(f"最佳匹配号码: {best_actual_numbers}")
    print(f"最大命中数量: {max_hit_count}")
    print(f"红球命中: {best_hit_info['red_hits']}, 蓝球命中: {best_hit_info['blue_hits']}")
    print(f"是否中奖: {'是' if best_hit_info['is_hit'] else '否'}")
    
    # 验证逻辑是否正确
    expected_period = 8  # 期号8有最多的命中数（7个）
    if best_period == expected_period:
        print(f"\n✓ 测试通过：正确选择了期号{expected_period}（命中数最多）")
    else:
        print(f"\n✗ 测试失败：应该选择期号{expected_period}，但实际选择了期号{best_period}")

def test_tie_breaking_logic():
    """
    测试平局时选择最近期号的逻辑
    """
    print("\n" + "="*50)
    print("测试平局时选择最近期号的逻辑...")
    
    # 模拟预测号码
    predicted_numbers = [1, 5, 10, 15, 20, 25, 8]
    
    # 模拟数据：期号2和期号5都命中3个号码
    actual_data = [
        [2, 6, 11, 16, 21, 26, 9],   # 期号1：命中0个
        [1, 5, 10, 16, 21, 26, 9],   # 期号2：命中3个红球
        [1, 6, 11, 16, 21, 26, 9],   # 期号3：命中1个红球
        [2, 6, 11, 16, 21, 26, 9],   # 期号4：命中0个
        [1, 5, 10, 16, 21, 26, 9],   # 期号5：命中3个红球（与期号2相同）
        [1, 6, 11, 16, 21, 26, 9],   # 期号6：命中1个红球
    ]
    
    def check_hit_rate_test(predicted, actual):
        pred_red = set(predicted[:6])
        pred_blue = set(predicted[6:7])
        actual_red = set(actual[:6])
        actual_blue = set(actual[6:7])

        red_hits = len(pred_red & actual_red)
        blue_hits = len(pred_blue & actual_blue)
        is_hit = red_hits >= 5 and blue_hits == 1

        return {
            'red_hits': red_hits,
            'blue_hits': blue_hits,
            'total_hits': red_hits + blue_hits,
            'is_hit': is_hit
        }
    
    best_hit_info = None
    best_period = None
    max_hit_count = -1
    closest_period_index = None
    
    print(f"预测号码: {predicted_numbers}")
    print("\n逐期比对结果:")
    
    for j in range(1, 7):  # 检查6期数据
        actual_numbers = actual_data[j-1]
        hit_info = check_hit_rate_test(predicted_numbers, actual_numbers)
        current_hit_count = hit_info['total_hits']
        
        print(f"期号{j}: 实际号码{actual_numbers}, 命中{current_hit_count}个")
        
        if (current_hit_count > max_hit_count or 
            (current_hit_count == max_hit_count and (closest_period_index is None or j < closest_period_index))):
            max_hit_count = current_hit_count
            best_hit_info = hit_info
            best_period = j
            closest_period_index = j
    
    print(f"\n最终选择结果:")
    print(f"最佳匹配期号: {best_period}")
    print(f"最大命中数量: {max_hit_count}")
    
    # 验证逻辑：应该选择期号2（更近的期号）
    expected_period = 2
    if best_period == expected_period:
        print(f"\n✓ 测试通过：在平局情况下正确选择了更近的期号{expected_period}")
    else:
        print(f"\n✗ 测试失败：应该选择期号{expected_period}，但实际选择了期号{best_period}")

if __name__ == "__main__":
    test_hit_comparison_logic()
    test_tie_breaking_logic()
    print("\n" + "="*50)
    print("测试完成！")
