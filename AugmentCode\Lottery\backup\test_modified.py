#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试修改后的彩票分析程序
使用更早的数据进行测试，增加命中可能性
"""

from lottery_analysis_four_steps import LotteryAnalyzer

def test_modified_program():
    """测试修改后的程序功能"""
    print("=" * 60)
    print("测试修改后的彩票分析程序")
    print("=" * 60)
    
    # 创建分析器
    analyzer = LotteryAnalyzer()
    
    # 第一步：读取数据
    if not analyzer.step1_read_and_sort_data():
        return
    
    # 设置测试参数（使用更早的数据）
    analyzer.lottery_type = "SSQ"
    analyzer.data = analyzer.ssqhistory_allout
    analyzer.start_row = 500  # 使用更早的数据
    analyzer.method = "multi_bayesian"
    
    print(f"测试设置：")
    print(f"  彩票类型: {analyzer.lottery_type}")
    print(f"  开始行数: {analyzer.start_row}")
    print(f"  计算方法: {analyzer.method}")
    print(f"  数据总行数: {len(analyzer.data)}")
    
    # 进行20次测试
    print(f"\n开始进行20次测试（每次与后续6期比对）...")
    analyzer.results = []
    hit_count = 0
    hit_in_6_count = 0
    
    for i in range(20):
        current_row = analyzer.start_row + i
        
        # 检查是否有足够的后续数据
        if current_row + 6 >= len(analyzer.data):
            print(f"第{i+1}次：后续数据不足6期，停止测试")
            break
        
        # 获取训练数据
        train_data = analyzer.data.iloc[:current_row].copy()
        
        # 预测号码
        predicted_numbers = analyzer.predict_numbers(train_data, analyzer.method)
        
        if predicted_numbers is None:
            continue
        
        # 与后续6期数据进行比对
        hit_found = False
        best_hit_info = None
        best_target_period = None
        best_actual_numbers = None
        hit_period = 0
        
        for j in range(1, 7):  # 检查后续6期数据
            target_row = current_row + j
            actual_numbers = analyzer.data.iloc[target_row, 1:].values.tolist()
            
            # 检查命中情况
            hit_info = analyzer.check_hit_rate(predicted_numbers, actual_numbers)
            
            if hit_info['is_hit']:
                hit_found = True
                best_hit_info = hit_info
                best_target_period = analyzer.data.iloc[target_row, 0]
                best_actual_numbers = actual_numbers
                hit_period = j
                break
        
        # 如果没有命中，记录与第一期的比对结果
        if not hit_found:
            target_row = current_row + 1
            actual_numbers = analyzer.data.iloc[target_row, 1:].values.tolist()
            hit_info = analyzer.check_hit_rate(predicted_numbers, actual_numbers)
            best_target_period = analyzer.data.iloc[target_row, 0]
            best_actual_numbers = actual_numbers
            best_hit_info = hit_info
        
        # 记录结果
        result = {
            'iteration': i + 1,
            'base_period': analyzer.data.iloc[current_row - 1, 0],
            'target_period': best_target_period,
            'method': analyzer.method,
            'predicted_numbers': predicted_numbers,
            'actual_numbers': best_actual_numbers,
            'red_hits': best_hit_info['red_hits'],
            'blue_hits': best_hit_info['blue_hits'],
            'total_hits': best_hit_info['total_hits'],
            'is_hit': best_hit_info['is_hit'],
            'hit_in_6_periods': hit_found
        }
        
        analyzer.results.append(result)
        
        # 统计命中情况
        if best_hit_info['is_hit']:
            hit_count += 1
            print(f"第{i+1}次：*** 命中！*** 基于期号{result['base_period']}，在第{hit_period}期后命中期号{best_target_period}")
            print(f"  预测: {predicted_numbers}")
            print(f"  实际: {best_actual_numbers}")
            print(f"  命中: 红球{best_hit_info['red_hits']}个, 蓝球{best_hit_info['blue_hits']}个")
        
        if hit_found:
            hit_in_6_count += 1
        
        # 每5次显示一次进度
        if (i + 1) % 5 == 0:
            print(f"已完成 {i + 1}/20 次测试，命中次数: {hit_count}，6期内命中次数: {hit_in_6_count}")
    
    print(f"\n测试完成！")
    print(f"总测试次数: {len(analyzer.results)}")
    print(f"总命中次数: {hit_count}")
    print(f"6期内命中次数: {hit_in_6_count}")
    print(f"命中率: {hit_count/len(analyzer.results)*100:.2f}%" if analyzer.results else "0%")
    print(f"6期内命中率: {hit_in_6_count/len(analyzer.results)*100:.2f}%" if analyzer.results else "0%")
    
    # 保存结果
    if analyzer.step4_save_results_to_excel():
        print("\n测试结果已保存到Excel文件")

if __name__ == "__main__":
    test_modified_program()
