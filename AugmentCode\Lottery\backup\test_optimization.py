#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试优化后的彩票分析程序
"""

import pandas as pd
import numpy as np
from lottery_calc_optimization import LotteryAnalyzer

def test_weight_optimization():
    """测试权重优化功能"""
    print("=" * 60)
    print("测试权重优化功能")
    print("=" * 60)
    
    # 创建分析器实例
    analyzer = LotteryAnalyzer()
    
    # 创建模拟数据进行测试
    print("创建模拟数据...")
    
    # 模拟SSQ数据
    np.random.seed(42)  # 设置随机种子以便重现结果
    
    # 生成200期模拟数据
    periods = 200
    ssq_data = []
    
    for i in range(periods):
        period_num = 2024001 + i
        # 生成6个红球（1-33）
        red_balls = sorted(np.random.choice(range(1, 34), 6, replace=False))
        # 生成1个蓝球（1-16）
        blue_ball = np.random.choice(range(1, 17), 1)[0]
        
        row = [period_num] + list(red_balls) + [blue_ball]
        ssq_data.append(row)
    
    # 创建DataFrame
    columns = ['期号', '红球1', '红球2', '红球3', '红球4', '红球5', '红球6', '蓝球']
    ssq_df = pd.DataFrame(ssq_data, columns=columns)
    
    print(f"生成了{len(ssq_df)}期模拟数据")
    print("前5期数据:")
    print(ssq_df.head())
    
    # 设置分析器的数据
    analyzer.ssqhistory_allout = ssq_df
    analyzer.lottery_type = "SSQ"
    analyzer.data = ssq_df
    
    # 测试权重优化
    print("\n开始测试权重优化...")
    train_data = ssq_df.iloc[:150]  # 使用前150期作为训练数据
    
    try:
        # 测试权重优化
        optimal_weights = analyzer.optimize_bayesian_weights(train_data, validation_periods=6)
        print(f"优化后的权重: {optimal_weights}")
        
        # 测试使用优化权重进行预测
        predicted_numbers = analyzer.bayesian_prediction_with_weights(train_data, optimal_weights)
        print(f"预测号码: {predicted_numbers}")
        
        # 测试评估方法
        score = analyzer.evaluate_weights(train_data, optimal_weights, 6)
        print(f"权重评估得分: {score:.4f}")
        
        print("权重优化测试完成！")
        
    except Exception as e:
        print(f"权重优化测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_bayesian_prediction():
    """测试优化后的贝叶斯预测"""
    print("\n" + "=" * 60)
    print("测试优化后的贝叶斯预测")
    print("=" * 60)
    
    # 创建分析器实例
    analyzer = LotteryAnalyzer()
    
    # 使用之前的模拟数据
    np.random.seed(42)
    periods = 200
    ssq_data = []
    
    for i in range(periods):
        period_num = 2024001 + i
        red_balls = sorted(np.random.choice(range(1, 34), 6, replace=False))
        blue_ball = np.random.choice(range(1, 17), 1)[0]
        row = [period_num] + list(red_balls) + [blue_ball]
        ssq_data.append(row)
    
    columns = ['期号', '红球1', '红球2', '红球3', '红球4', '红球5', '红球6', '蓝球']
    ssq_df = pd.DataFrame(ssq_data, columns=columns)
    
    # 设置分析器的数据
    analyzer.ssqhistory_allout = ssq_df
    analyzer.lottery_type = "SSQ"
    analyzer.data = ssq_df
    
    # 测试优化后的贝叶斯预测
    train_data = ssq_df.iloc[:150]
    
    try:
        print("测试原始贝叶斯预测...")
        # 使用默认权重
        default_weights = analyzer.optimal_weights["SSQ"]
        predicted_default = analyzer.bayesian_prediction_with_weights(train_data, default_weights)
        print(f"默认权重预测: {predicted_default}")
        
        print("\n测试优化后的贝叶斯预测...")
        # 使用优化后的预测方法
        predicted_optimized = analyzer.bayesian_prediction_ai(train_data)
        print(f"优化权重预测: {predicted_optimized}")
        
        # 比较预测结果
        actual_numbers = ssq_df.iloc[150, 1:].values.tolist()
        print(f"实际号码: {actual_numbers}")
        
        # 计算命中情况
        hit_default = analyzer.check_hit_rate(predicted_default, actual_numbers)
        hit_optimized = analyzer.check_hit_rate(predicted_optimized, actual_numbers)
        
        print(f"默认权重命中: 红球{hit_default['red_hits']}个, 蓝球{hit_default['blue_hits']}个, 总计{hit_default['total_hits']}个")
        print(f"优化权重命中: 红球{hit_optimized['red_hits']}个, 蓝球{hit_optimized['blue_hits']}个, 总计{hit_optimized['total_hits']}个")
        
        print("贝叶斯预测测试完成！")
        
    except Exception as e:
        print(f"贝叶斯预测测试失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主测试函数"""
    print("开始测试优化后的彩票分析程序")
    print("=" * 60)
    
    # 测试权重优化功能
    test_weight_optimization()
    
    # 测试优化后的贝叶斯预测
    test_bayesian_prediction()
    
    print("\n" + "=" * 60)
    print("所有测试完成！")
    print("=" * 60)

if __name__ == "__main__":
    main()
