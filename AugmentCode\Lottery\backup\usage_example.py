#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
优化后的彩票分析程序使用示例
"""

from lottery_calc_optimization import LotteryAnalyzer

def example_usage():
    """使用示例"""
    print("=" * 80)
    print("优化后的彩票分析程序使用示例")
    print("=" * 80)
    
    # 创建分析器实例
    analyzer = LotteryAnalyzer("lottery_data_all.xlsx")
    
    print("\n📋 使用步骤:")
    print("1. 确保 lottery_data_all.xlsx 文件存在")
    print("2. 运行程序: python lottery_calc_optimization.py")
    print("3. 按照提示选择参数:")
    print("   - 彩票类型: 1 (SSQ) 或 2 (DLT)")
    print("   - 开始行数: 建议 2000")
    print("   - 计算方法: 4 (基于贝叶斯预测) - 启用权重优化")
    
    print("\n🎯 贝叶斯预测方法优化特点:")
    print("- ✅ 自动权重优化")
    print("- ✅ 6期数据验证")
    print("- ✅ 比对期数优化 (6期)")
    print("- ✅ 权重参数保存")
    print("- ✅ 命中精度提升")
    
    print("\n⚙️ 权重优化过程:")
    print("1. 程序自动分析历史数据")
    print("2. 搜索最优权重组合 (49种组合)")
    print("3. 使用6期数据验证性能")
    print("4. 保存最优权重到 optimal_weights.json")
    print("5. 应用优化权重进行预测")
    
    print("\n📊 输出结果:")
    print("- 详细的预测与比对结果")
    print("- Excel格式的分析报告")
    print("- 权重优化信息")
    print("- 命中率统计")
    
    print("\n💡 提示:")
    print("- 首次运行会进行权重优化，耗时较长")
    print("- 后续运行会自动加载已优化的权重")
    print("- 权重文件 optimal_weights.json 可以备份")
    print("- 建议使用足够的历史数据 (>2000期)")

def show_optimization_benefits():
    """展示优化效果"""
    print("\n" + "=" * 60)
    print("优化效果对比")
    print("=" * 60)
    
    print("\n📈 优化前 vs 优化后:")
    print("┌─────────────────┬──────────────┬──────────────┐")
    print("│ 项目            │ 优化前       │ 优化后       │")
    print("├─────────────────┼──────────────┼──────────────┤")
    print("│ 权重设置        │ 固定 (0.7:0.3)│ 自动优化     │")
    print("│ 比对期数        │ 12期         │ 6期          │")
    print("│ 权重调整        │ 手动         │ 自动         │")
    print("│ 验证机制        │ 无           │ 6期验证      │")
    print("│ 参数保存        │ 无           │ 自动保存     │")
    print("│ 学习能力        │ 静态         │ 自适应       │")
    print("│ 命中精度        │ 基础         │ 提升         │")
    print("└─────────────────┴──────────────┴──────────────┘")
    
    print("\n🎯 核心改进:")
    print("1. 动态权重优化: 根据历史数据自动寻找最优权重组合")
    print("2. 精准验证: 使用6期数据验证权重性能")
    print("3. 智能比对: 贝叶斯方法专用6期比对")
    print("4. 持久化存储: 最优权重自动保存和加载")
    print("5. 评分机制: 命中数量 + 中奖标准双重评分")

def main():
    """主函数"""
    example_usage()
    show_optimization_benefits()
    
    print("\n" + "=" * 80)
    print("🚀 开始使用优化后的程序:")
    print("python lottery_calc_optimization.py")
    print("=" * 80)

if __name__ == "__main__":
    main()
