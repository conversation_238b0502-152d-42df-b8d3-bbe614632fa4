#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
彩票分析四步程序运行示例
演示如何使用lottery_analysis_four_steps.py程序
"""

from lottery_analysis_four_steps import LotteryAnalysisFourSteps

def example_usage():
    """
    示例：如何使用四步彩票分析程序
    """
    print("彩票分析四步程序使用示例")
    print("=" * 50)
    
    # 创建分析器实例
    analyzer = LotteryAnalysisFourSteps("lottery_data_all.xlsx")
    
    print("方法1：运行完整的四步流程（交互式）")
    print("-" * 30)
    print("analyzer.run_four_steps_analysis()")
    print("# 这将启动交互式程序，用户需要输入选择")
    
    print("\n方法2：程序化设置参数（自动化）")
    print("-" * 30)
    
    # 第一步：读取数据
    print("第一步：读取数据")
    if analyzer.step1_read_and_sort_data():
        print("✓ 数据读取成功")
        
        # 第二步：设置参数（程序化方式）
        print("\n第二步：设置参数")
        analyzer.lottery_type = "SSQ"  # 选择双色球
        analyzer.selected_data = analyzer.ssqhistory_allout
        analyzer.start_row = 2000  # 从第2000行开始
        analyzer.calculation_method = "集成预测"  # 使用集成预测
        print(f"✓ 参数设置完成：{analyzer.lottery_type}, 开始行{analyzer.start_row}, {analyzer.calculation_method}")
        
        # 第三步：计算（可选择小规模测试）
        print("\n第三步：计算验证")
        print("选择：")
        print("a) 小规模测试（10次预测）")
        print("b) 完整计算（500次预测）")
        
        choice = input("请选择 (a/b): ").strip().lower()
        
        if choice == 'a':
            # 小规模测试
            print("执行小规模测试...")
            test_small_scale(analyzer)
        else:
            # 完整计算
            print("执行完整计算...")
            if analyzer.step3_calculation_and_verification():
                print("✓ 计算完成")
                
                # 第四步：保存结果
                print("\n第四步：保存结果")
                if analyzer.step4_save_results():
                    print("✓ 结果保存成功")
    
    print("\n示例完成！")

def test_small_scale(analyzer):
    """
    小规模测试示例
    """
    print("开始小规模测试...")
    
    # 保存原始参数
    original_start_row = analyzer.start_row
    
    # 设置测试参数
    analyzer.start_row = 100
    test_iterations = 10
    
    results = []
    for i in range(test_iterations):
        current_row = analyzer.start_row + i
        
        if current_row + 6 >= len(analyzer.selected_data):
            break
        
        # 获取训练数据
        training_data = analyzer.selected_data.iloc[:current_row].copy()
        
        # 预测
        prediction = analyzer.predict_numbers(training_data)
        
        # 验证
        actual_data = analyzer.selected_data.iloc[current_row]
        match_info = analyzer.check_prediction_accuracy(prediction, actual_data)
        
        results.append({
            'iteration': i + 1,
            'prediction_period': int(training_data.iloc[-1, 0]),
            'actual_period': int(actual_data.iloc[0]),
            'predicted_red': prediction['red_numbers'],
            'predicted_blue': prediction['blue_numbers'],
            'actual_red': match_info['actual_red'],
            'actual_blue': match_info['actual_blue'],
            'total_matches': match_info['total_matches'],
            'is_hit': match_info['is_hit']
        })
        
        print(f"第{i+1}次: 期号{results[-1]['actual_period']}, "
              f"预测{results[-1]['predicted_red']}+{results[-1]['predicted_blue']}, "
              f"实际{results[-1]['actual_red']}+{results[-1]['actual_blue']}, "
              f"匹配{results[-1]['total_matches']}个, "
              f"{'命中' if results[-1]['is_hit'] else '未命中'}")
    
    # 恢复原始参数
    analyzer.start_row = original_start_row
    
    # 统计结果
    hit_count = sum(1 for r in results if r['is_hit'])
    avg_matches = sum(r['total_matches'] for r in results) / len(results) if results else 0
    
    print(f"\n小规模测试结果:")
    print(f"总预测次数: {len(results)}")
    print(f"命中次数: {hit_count}")
    print(f"命中率: {hit_count/len(results)*100:.2f}%" if results else "0%")
    print(f"平均匹配数: {avg_matches:.2f}")

def demonstrate_prediction_methods():
    """
    演示不同预测方法的使用
    """
    print("\n预测方法演示")
    print("=" * 30)
    
    analyzer = LotteryAnalysisFourSteps("lottery_data_all.xlsx")
    
    if analyzer.step1_read_and_sort_data():
        # 设置为双色球
        analyzer.lottery_type = "SSQ"
        analyzer.selected_data = analyzer.ssqhistory_allout
        
        # 使用前1000期数据作为训练集
        training_data = analyzer.selected_data.iloc[:1000]
        
        print(f"使用前{len(training_data)}期数据进行预测演示:")
        
        # 贝叶斯预测
        print("\n1. 贝叶斯预测:")
        bayesian_result = analyzer.bayesian_prediction(training_data)
        print(f"   红球: {bayesian_result['red_numbers']}")
        print(f"   蓝球: {bayesian_result['blue_numbers']}")
        
        # 马尔可夫预测
        print("\n2. 马尔可夫预测:")
        markov_result = analyzer.markov_prediction(training_data)
        print(f"   红球: {markov_result['red_numbers']}")
        print(f"   蓝球: {markov_result['blue_numbers']}")
        
        # 集成预测
        print("\n3. 集成预测:")
        ensemble_result = analyzer.ensemble_prediction(training_data)
        print(f"   红球: {ensemble_result['red_numbers']}")
        print(f"   蓝球: {ensemble_result['blue_numbers']}")
        
        # 与实际第1001期对比
        if len(analyzer.selected_data) > 1000:
            actual_1001 = analyzer.selected_data.iloc[1000]
            actual_red = actual_1001.iloc[1:7].tolist()
            actual_blue = [actual_1001.iloc[7]]
            
            print(f"\n实际第{int(actual_1001.iloc[0])}期开奖:")
            print(f"   红球: {actual_red}")
            print(f"   蓝球: {actual_blue}")
            
            # 计算各方法的匹配度
            for method_name, result in [
                ("贝叶斯", bayesian_result),
                ("马尔可夫", markov_result), 
                ("集成", ensemble_result)
            ]:
                red_matches = len(set(result['red_numbers']) & set(actual_red))
                blue_matches = len(set(result['blue_numbers']) & set(actual_blue))
                total_matches = red_matches + blue_matches
                print(f"   {method_name}方法匹配: 红球{red_matches}个, 蓝球{blue_matches}个, 总计{total_matches}个")

if __name__ == "__main__":
    print("彩票分析四步程序运行示例")
    print("=" * 50)
    
    print("请选择运行模式:")
    print("1. 完整示例（包含用户交互）")
    print("2. 预测方法演示")
    print("3. 直接运行完整程序")
    
    choice = input("请输入选择 (1/2/3): ").strip()
    
    if choice == "1":
        example_usage()
    elif choice == "2":
        demonstrate_prediction_methods()
    elif choice == "3":
        analyzer = LotteryAnalysisFourSteps("lottery_data_all.xlsx")
        analyzer.run_four_steps_analysis()
    else:
        print("无效选择，运行默认示例...")
        example_usage()
